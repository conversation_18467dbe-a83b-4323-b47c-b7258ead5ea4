import { ReactNode } from "react";
import { defaultProps } from "~/default.spec";
export interface DocumentUploadProps extends defaultProps {
    id: string;
    existingFiles?: DocumentUploadFile[];
    shouldReset?: boolean;
    title: string;
    subtitle?: string;
    subtext?: string;
    username?: string;
    maxFiles?: number;
    maxFileSize?: number;
    acceptedFileTypes?: string[];
    onFileAccept?: (files: DocumentUploadFile[]) => void;
    onFileReject?: () => void;
    onFileDelete?: (files: DocumentUploadFile[]) => void;
    showDropZone?: boolean;
    showNoDocumentsBanner?: boolean;
    noDocumentsBannerText?: string;
    showFormRequiredFields?: boolean;
    children?: ReactNode;
    required?: boolean;
}
export interface DocumentUploadFile {
    id: string;
    time: string;
    file: File;
    isPending?: boolean;
}
