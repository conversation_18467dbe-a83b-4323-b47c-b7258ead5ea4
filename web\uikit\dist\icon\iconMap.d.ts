/// <reference types="react" />
export declare const iconMap: {
    "action-item-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "active-clock": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "add-document-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "add-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "add-icon-small": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "add-square-filled": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-down": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-download": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-left": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-left-thicker": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-right": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-sort-down-line": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-up": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "arrow-upload": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "attach-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "base-filter-arrows": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "bdo-logo": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "bdo-white-logo": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "briefcase-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "calendar-ltr": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "calendar-placeholder": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "checkbox-checkmark-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    checkmark: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "checkmark-circle-filled": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "chevron-double": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "chevron-down": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "chevron-left": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "chevron-right": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "chevron-up": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "chevron-up-down": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "circle-filled": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "circle-half-fill": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "circle-line": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "client-logo": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "comment-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "close-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "close-icon-white": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "color-line": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "copy-document-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "complete-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "copy-elipses": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "copy-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "copy-select": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "copytoclipboard-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "default-profile-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "delayed-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "delete-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "delete-icon-red": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "delete-off-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "dismiss-circle-filled": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "dismiss-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "dismiss-icon-thicker": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-add": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-archive": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-code": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-copy-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-csv": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-data": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-doc": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-email": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-folder": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-folder-outline": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-pdf": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-png": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-ppt": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-video": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "document-visio": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "documents-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "dot-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    Drag: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    drag: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "drag-handle": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "dropdown-arrow-down": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "edit-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "ellipses-horizontal-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "ellipses-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "envelope-light": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "error-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "error-icon-red": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "error-circle": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "error-message-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "export-files": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "feedback-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "filter-down-arrow": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "filter-up-arrow": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "folder-arrow-right": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "folder-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "folder-icon-filled": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "folder-open-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "folder-zip-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "gear-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "hamburger-menu": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "help-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "high-priority": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "history-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "home-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "image-circle": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "info-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "info-icon-black-and-white": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "inprogress-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "left-arrow": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "link-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "lock-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "low-priority": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "mail-black": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "mail-dismiss": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "mail-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "move-document-file-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "move-document-to-recycle-bin-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "open-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "pause-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "pen-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "people-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "people-search": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "person-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "person-filled": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "person-warning-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "phone-light": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "play-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "prohibited-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "projects-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "read-only": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "recycle-bin": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "rename-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "restore-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "right-arrow": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "right-chevron-breadcrumb": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "search-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "send-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "sign-out": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "signature-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "signature-request": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "star-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "star-icon-yellow": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "status-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "success-checkmark": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "tasks-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "tasks-square-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "task-ltr-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "teams-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "text-box": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "typing-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "up-arrow": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "upcoming-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "updated-burger": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "warning-icon": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "warning-icon-solid": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    alert: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    chevron: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    circle: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    info: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    priority: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "cash-flow": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    compliance: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "increasing-brand-awareness": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    regulation: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "financial-management": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "supply-chains": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "data-security": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "talent-strategy": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    technology: import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "considerations-other": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "considerations-triangle": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
    "did-you-know": import("react").FunctionComponent<import("react").SVGProps<SVGSVGElement>>;
};
