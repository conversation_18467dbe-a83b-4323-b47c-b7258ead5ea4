.inputWrapper {
  width: 100%;
  position: relative;
  margin-bottom: 1rem;
}

.inputField {
  background-color: var(--color-bg-white);
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 2px;
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body);
  font-family: inherit;
  line-height: var(--lineheight-body);
  height: 48px;
  box-sizing: border-box;
  transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.inputField:read-only {
  cursor: pointer;
}

.inputThin {
  height: auto;
  padding: 0.5rem;
}

.searchTerm {
  border: 1px solid var(--color-primary-charcoal);
}

.inputField:disabled {
  background-color: var(--color-bg-light-grey);
  border-color: var(--color-border-pale-grey);
  color: var(--color-bg-inactive-charcoal);
  cursor: not-allowed;
}

.baseAlign {
  padding: 1rem;
  padding-top: 2.25rem;
}

.centerAlign {
  padding: 1.625rem 1rem;
}

.inputField:focus {
  outline: none;
  border: 1px solid var(--color-primary-charcoal);

  &:global(.focusedByKeyboard) {
    border: 1px solid var(--color-border-pale-grey);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }
}

.disappearingLabel,
.floatingLabel {
  position: absolute;
  top: 1rem;
  left: 1rem;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-bg-inactive-charcoal);
  pointer-events: none;
  z-index: 1;
}

.floatingLabel {
  transition: 0.2s ease-in-out;
}

.disappearingLabel {
  visibility: visible;
  opacity: 1;
  transition: opacity 2s linear;
  transform: translate(15px, -10px);
}

.floatingLabelActive {
  top: 8px;
  font-size: var(--fontsize-body-xsmall);
  line-height: var(--lineheight-body-xsmall);
  color: var(--color-primary-light-charcoal);
}

.inputWrapper:focus-within .floatingLabel,
.inputWrapper input:not(:placeholder-shown) + .floatingLabel {
  top: 8px;
  font-size: var(--fontsize-body-xsmall);
  line-height: var(--lineheight-body-xsmall);
}

.hidden {
  display: none;
}

.inputError {
  border: 1px solid var(--color-primary-red);
}

.errorTextContainer {
  display: flex;
  gap: 0.5rem;
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  margin-top: 0.75rem;
}

.additionalErrorMessage {
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
}

.errorText {
  margin: 0;
  line-height: 1;
  text-align: left;
  font-size: var(--fontsize-body-small);
}

.iconWrapper {
  position: absolute;
  color: var(--color-primary-slate);
  top: 16px;
  left: 10px;
  transition: opacity 2s linear;
}

.requiredFieldsTextAsterisk {
  font-size: var(--fontsize-body);
  color: var(--color-primary-red);
}

.searchIconWrapper {
  position: absolute;
  right: 25px;
  top: 14px;

  svg {
    path {
      fill: var(--color-primary-charcoal);
    }
  }
}

@media (min-width: 769px) {
  .inputWrapper {
    margin-bottom: 0;
  }
}
