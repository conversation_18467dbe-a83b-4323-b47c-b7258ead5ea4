.attachmentHistory-module__attachmentHistoryWrapper___K67qx {
  background-color: var(--color-bg-white);
  border: 1px solid var(--color-primary-pale-charcoal);
  padding: 1rem;
  border-radius: 2px;

  strong {
    color: var(--color-primary-charcoal);
    line-height: var(--lineheight-body);
  }

  .attachmentHistory-module__leftIconWrapper___X9gma {
    width: min-content;
    padding: 0.5rem;
    background-color: var(--color-bg-light-grey);
    border-radius: 2px;

    svg {
      path {
        fill: var(--color-primary-slate);
      }
    }
  }

  .attachmentHistory-module__historyItems___apx62 {
    margin: 1rem 0 0;
    padding: 0;

    .attachmentHistory-module__leftHandInfo___b3mBU,
    .attachmentHistory-module__rightHandInfo___KhzFs,
    .attachmentHistory-module__historyItem___4bGmG {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    > :first-child {
      border-top: 1px solid var(--color-primary-pale-charcoal);
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
    }

    > :last-child {
      border-bottom-left-radius: 2px;
      border-bottom-right-radius: 2px;
    }

    .attachmentHistory-module__historyItem___4bGmG {
      align-items: center;
      border-left: 1px solid var(--color-primary-pale-charcoal);
      border-right: 1px solid var(--color-primary-pale-charcoal);
      border-bottom: 1px solid var(--color-primary-pale-charcoal);

      column-gap: 0.75rem;
      display: grid;
      grid-template-columns: 1fr auto;
      padding: 0.75rem 1rem;

      .attachmentHistory-module__leftHandInfo___b3mBU {
        gap: 0.75rem;
        min-width: 0;
        min-height: 0;

        .attachmentHistory-module__pipe___-F14h {
          color: var(--color-border-grey);
          margin: 0 0.5rem;
        }

        .attachmentHistory-module__basicInfo___ZrMc9 {
          color: var(--color-bg-light-charcoal);
        }
      }

      .attachmentHistory-module__rightHandInfo___KhzFs {
        color: var(--color-bg-inactive-charcoal);
        gap: 0.5rem;

        svg {
          cursor: pointer;
          color: var(--color-primary-charcoal);
        }
      }

      .attachmentHistory-module__rightHandInfo--primary___BE-ck {
        svg {
          color: var(--color-bg-white);
        }
      }

      .attachmentHistory-module__rightHandInfo--secondary___O3W7A {
        svg {
          color: var(--color-primary-charcoal);
        }
      }

      .attachmentHistory-module__documentName___9a7td {
        color: var(--color-primary-charcoal);
        font-size: var(--fontsize-body);
        line-height: var(--lineheight-body);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

@media (min-width: 769px) {
  .attachmentHistory-module__historyItem___4bGmG {
    min-width: 520px;
  }
}

.attachmentHistory-module__nameContainer___VXBuy {
  font-size: var(--fontsize-body-small);
  line-height: var(--fontsize-body-small);
  color: var(--color-bg-inactive-charcoal);
  justify-self: end;
  white-space: nowrap;
}

.attachmentHistory-module__title___BZ-Lu {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 600;
}

.attachmentHistory-module__downloadIcon___6wdlD {
  padding: 0.5rem;
  border-radius: 50%;

  &:hover {
    background-color: var(--color-bg-light-grey);
  }

  &:focus-visible {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }
}

.attachmentHistory-module__noDocumentsUploaded___0fs2x {
  padding: 1rem 1.5rem;
  border-radius: 2px;
  background-color: #fafafa;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-primary-inactive-charcoal);
  text-align: center;
  margin-top: 1rem;
}

.icon-module__iconComponent___ssGnu {
  display: flex;
  align-items: center;
}
.button-module__button___YrVJX {
  border-radius: 2px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-style: normal;
  font-weight: 600;
  line-height: var(--lineheight-body);
  gap: 0.5rem;
  font-size: var(--fontsize-body);
  cursor: pointer;
  font-family: "ProximaNova", Arial, sans-serif;
  transition: background-color 0.2s;
}

.button-module__button___YrVJX:focus-visible {
  outline-offset: 2px;
  outline: 3px solid var(--color-secondary-cobalt);
}

.button-module__button___YrVJX.button-module__large___jSy8V {
  padding: 0.5rem 1.5rem;
  height: 56px;
}

.button-module__button___YrVJX.button-module__small___7YW5d {
  padding: 0.5rem 1rem;
  height: 40px;
}

.button-module__button___YrVJX.button-module__long___9LkWB {
  width: 208px;
  height: 44px;
  text-align: left;
}

.button-module__button___YrVJX.button-module__primary___2nlh8:disabled,
.button-module__button___YrVJX.button-module__primary___2nlh8:disabled:hover {
  cursor: not-allowed;
  background-color: var(--color-primary-pale-charcoal);
  color: var(--color-bg-inactive-charcoal);
  path {
    fill: var(--color-bg-inactive-charcoal);
  }
}

.button-module__button___YrVJX.button-module__primary___2nlh8 {
  border: none;
  color: var(--color-bg-white);
  background-color: var(--color-primary-red);
}

.button-module__button___YrVJX.button-module__primary___2nlh8:hover {
  background-color: var(--color-primary-slate);
}

.button-module__button___YrVJX.button-module__secondary___8gbTk {
  border: 1px solid var(--color-primary-charcoal);
  background-color: var(--color-bg-white);
  color: var(--color-primary-charcoal);
  path {
    fill: var(--color-primary-charcoal);
  }
}

.button-module__button___YrVJX.button-module__secondary___8gbTk:hover {
  background-color: var(--color-primary-charcoal);
  color: var(--color-bg-white);
  path {
    fill: var(--color-bg-white);
  }
}

.button-module__button___YrVJX.button-module__secondary___8gbTk:disabled,
.button-module__button___YrVJX.button-module__secondary___8gbTk:disabled:hover {
  cursor: not-allowed;
  border: 1px solid var(--color-primary-pale-charcoal);
  color: var(--color-bg-inactive-charcoal);
  background-color: transparent;
  path {
    fill: var(--color-bg-inactive-charcoal);
  }
}

.button-module__button___YrVJX.button-module__tertiary___I32Jg {
  border: none;
  color: var(--color-primary-charcoal);
  background-color: transparent;
  path {
    fill: var(--color-primary-charcoal);
  }

  &:hover {
    text-decoration: underline;
  }
}

.button-module__button___YrVJX.button-module__tertiary___I32Jg:disabled,
.button-module__button___YrVJX.button-module__tertiary___I32Jg:disabled:hover {
  cursor: not-allowed;
  color: var(--color-primary-inactive-charcoal);
  path {
    fill: var(--color-primary-inactive-charcoal);
  }
}

.button-module__button___YrVJX.button-module__warningButton___gUstU {
  color: var(--color-secondary-burgundy);
  svg {
    path {
      fill: var(--color-secondary-burgundy);
    }
  }
}

.button-module__iconWrapper___BCrPj {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.button-module__iconWrapper___BCrPj.button-module__small___7YW5d {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.button-module__button___YrVJX.button-module__border___8WoN8 {
  border: 1px solid var(--color-primary-charcoal);
}
.button-module__button___YrVJX.button-module__noBorder___g2A55 {
  border: none;
}

.button-module__isHighLighted___W0kGv {
  background-color: var(--color-bg-light-grey) !important;
  color: var(--color-primary-light-charcoal) !important;
}

.button-module__spinner___-IXcL {
  display: flex;
  padding: 0 1rem;
}

.button-module__spinnerWithLabel___3QIxn {
  padding: 0;
}

.spinner-module__loader___wXiJo {
  border: 5px solid var(--color-primary-red);
  border-top-color: var(--color-border-white);
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: spinner-module__rotation___g99zR 1s linear infinite;
}

.spinner-module__dropdownLoader___Azm1k {
  border-color: var(--color-primary-red);
  border-top-color: var(--color-bg-light-grey);
}

.spinner-module__tableLoader___wc-S5 {
  border-color: var(--color-primary-red);
  border-top-color: var(--color-bg-light-grey);
}

.spinner-module__buttonLoader___eLwVY {
  border-color: var(--color-primary-red);
  border-top-color: var(--color-primary-pale-charcoal);
}

@keyframes spinner-module__rotation___g99zR {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spinner-module__spinnerWrapper___U9V1m {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}

.spinner-module__spinnerWrapper___U9V1m::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  z-index: var(--z-index-spinner);
}

.spinner-module__spinnerCentered___R1zF- {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-spinner);
}

.chip-module__chip___oBqxy {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 2px;
  border: 1px solid var(--color-border-grey);
  text-wrap-mode: nowrap;
}

.chip-module__chip--priority___NZvsn,
.chip-module__chip--request___D-qms {
  padding: 0.25rem 0.5rem 0.25rem 0.25rem;
  background-color: var(--color-bg-light-grey);
  border: none;
}

.chip-module__chip--readOnly___-nsx0 {
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
  border: 1px solid var(--color-border-grey);
  gap: 0.25rem;
  width: fit-content;
  background-color: transparent;
}

.chip-module__chip--warning___8-1Kp {
  padding: 0.25rem 0.5rem;
  border-radius: 2px;
  border: 1px solid var(--color-secondary-gold);
}

.chip-module__chip--error___W42N9 {
  border: none;
  border-radius: 0;
  border-bottom: 1px solid var(--color-secondary-burgundy);
  padding: 0.25rem 0;
}

.chip-module__chip--action___Mmctx {
  grid-template-columns: auto 1fr;
  justify-content: start;
  border: none;
  border-radius: 100px;
  background-color: var(--color-bg-light-grey);
  color: var(--color-primary-slate);
  transition: all 100ms ease-out;
  width: fit-content;
  padding: 0.125rem 0.5rem 0.125rem 0.25rem;
}

.chip-module__chip--location___qJKYY {
  grid-template-columns: auto 1fr;
  justify-content: start;
  column-gap: 0.5rem;
  border: none;
  border-radius: 100px;
  padding: 0.125rem 0.5rem;
  background-color: var(--color-bg-light-grey);
  color: var(--color-primary-slate);
  transition: all 100ms ease-out;
}

.chip-module__chip--overview___M1ZQB {
  grid-template-columns: auto 1fr;
  justify-content: start;
  column-gap: 0.5rem;
  border: none;
  border-radius: 100px;
  padding: 0.125rem 0.5rem;
  background-color: white;
  color: var(--color-primary-slate);
  transition: all 100ms ease-out;
  border: 1px solid #e7e7e7;
}

.chip-module__chip--status___02PII {
  align-items: center;
  justify-content: start;
  column-gap: 0.5rem;
  border: none;
  border-radius: 2px;
  padding: 0.125rem 0.5rem;
  background-color: #e5eff8;
  color: var(--color-secondary-cobalt) !important;
  transition: all 100ms ease-out;
}

.chip-module__chip--dueDate___exnOK {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--color-bg-white);
  border-radius: 6.25rem;
  padding: 0.125rem 0.5rem;
  font-size: var(--fontsize-body-small);
  border: none;
}

.chip-module__textOnly___qOidK {
  grid-template-columns: none;
  padding: 0.25rem 0.5rem;
}

.chip-module__chip--location___qJKYY {
  &.chip-module__interactive___NvwkH {
    &:hover {
      background-color: var(--color-bg-primary-pale-charcoal);
      cursor: pointer;
    }
  }
}

.chip-module__buttonReset___CR9r6 {
  padding: 0;

  &:focus-visible {
    outline: none;

    .chip-module__chip--location___qJKYY {
      outline: 2px solid var(--color-secondary-cobalt);
      outline-offset: -2px;
    }
  }
}

/* TODO: remove when avatar component used when available */
.chip-module__avatar___Hw4aY {
  display: grid;
  align-items: center;
  justify-content: center;
  font-size: var(--fontsize-body-label);
  line-height: var(--lineheight-body-label);
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.5rem;
  border-radius: 9999px;
}

.chip-module__avatar--priority___CWgkZ,
.chip-module__avatar--request___mStdv {
  display: grid;
  align-items: center;
  justify-content: center;
  font-size: var(--fontsize-body-label);
  line-height: var(--lineheight-body-label);
  color: var(--color-bg-white);
  height: 1.25rem;
  border-radius: 2px;
}

/*
The two stylings below for request and location chip types are added to combat a bug relating to currentColor
where all chip icons were being set to have the color red, these should be removed eventually.
*/
.chip-module__avatar--request___mStdv {
  width: 1.25rem;
  svg {
    path {
      fill: white;
    }
  }
}

/* TODO - Remove if risk colors are fine */
/* .avatar--overview,
.avatar--location {
  svg {
    path {
      fill: var(--color-primary-slate);
    }
  }
} */

.chip-module__avatar--priority___CWgkZ {
  padding-right: 0.5rem;
  svg {
    path {
      fill: var(--color-primary-red);
    }
  }
}

.chip-module__avatar--request___mStdv:has([aria-label="up-arrow"]) {
  background-color: var(--color-secondary-ocean);
  margin-right: 0.5rem;
}

.chip-module__avatar--request___mStdv:has([aria-label="signature-request"]) {
  background-color: var(--color-secondary-jade);
  margin-right: 0.5rem;
}

.chip-module__avatar--warning___G2c7K {
  display: grid;
  align-items: center;
  justify-content: center;
  font-size: var(--fontsize-body-label);
  line-height: var(--lineheight-body-label);
  color: var(--color-bg-white);
  background-color: var(--color-secondary-ocean);
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.5rem;
  border-radius: 9999px;
  background-color: var(--color-secondary-gold);
}

.chip-module__avatar--error___9WvK9 {
  margin-right: 0.5rem;
}

.chip-module__avatar--action___LyUXs {
  background-color: none;
  padding-right: 4px;
  padding-left: 4px;
  align-items: center;
  color: var(--color-primary-light-slate);
}

.chip-module__avatar--status___KieJv {
  display: none;
}

.chip-module__text___lu-WO {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  font-weight: 600;
  color: var(--color-primary-light-charcoal);
}

.chip-module__text--request___mbOAK {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  font-weight: 400;
  color: var(--color-primary-charcoal);
  font-family: var(--primary-font-family);
}

.chip-module__text--readOnly___A12EK {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  font-weight: 400;
  color: var(--color-primary-inactive-charcoal);
  font-family: var(--primary-font-family);
}

.chip-module__text--priority___S4cdv {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  font-weight: 400;
  color: var(--color-primary-light-charcoal);
  font-family: var(--primary-font-family);
}

.chip-module__text--action___pQ3ZM {
  font-family: var(--primary-font-family);
  color: var(--color-primary-light-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chip-module__text--overview___5yQI7,
.chip-module__text--location___A2pLY {
  font-family: "ProximaNova", Arial, sans-serif;
  color: var(--color-primary-light-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
}

.chip-module__text--location___A2pLY {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chip-module__text--status___ZFZWF {
  font-family: "ProximaNova", Arial, sans-serif;
  color: var(--color-primary-light-charcoal);
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
  text-align: center;
  color: #0062b8;
}

.chip-module__dismissButton___BYZiH {
  display: grid;
  align-items: center;
  margin-left: 0.5rem;
  border: none;
  background: none;
  padding: 0;
  outline: 0;
  cursor: pointer;
  color: var(--color-primary-slate);
  padding: 0.25rem;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--color-bg-light-grey);
  }
}

.avatar-module__avatar___eebFp {
  border-radius: 50%;
  width: 4rem;
  height: 4rem;
  display: block;
}

.avatar-module__avatar--x-small___o0h9k {
  height: 1.5rem;
  width: 1.5rem;
  font-size: var(--fontsize-body-xsmall);
}

.avatar-module__avatar--small___Xyh-y {
  height: 2rem;
  width: 2rem;
  font-size: var(--fontsize-body-xsmall);
}

.avatar-module__avatar--small-medium___t75O2 {
  height: 2.5rem;
  width: 2.5rem;
  font-size: var(--fontsize-body-small);
}

.avatar-module__avatar--medium-small___j-FkF {
  height: 3.5rem;
  width: 3.5rem;
  font-size: var(--fontsize-body-small);
}

.avatar-module__avatar--medium___bg5Wa {
  height: 4rem;
  width: 4rem;
  font-size: var(--fontsize-body-small);
}

.avatar-module__avatar--large___eoMcN {
  height: 7.5rem;
  width: 7.5rem;
  font-weight: 600;
  font-size: var(--fontsize-h5);
}

.avatar-module__avatar--x-large___oqTYK {
  height: 9.375rem;
  width: 9.375rem;
  font-weight: 600;
  font-size: var(--fontsize-h5);
}

.avatar-module__clickable___DabDR {
  cursor: pointer;
}

.avatar-module__fallback___sXkqT {
  background-color: var(--color-bg-primary-pale-charcoal);
  color: var(--color-primary-charcoal);
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--fontsize-body-small);
  font-weight: 600;
}

.avatar-module__fallback--chip___Ix5MA {
  font-size: var(--fontsize-body-xsmall);
  width: 1rem;
  height: 1rem;
}

.avatar-module__fallback--x-small___xZzGD {
  font-size: 0.75rem;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.avatar-module__fallback--small___Bn-Rc {
  font-size: 1rem;
  width: 2rem;
  height: 2rem;
}

.avatar-module__fallback-small-medium___eY3Bq {
  height: 2.5rem;
  width: 2.5rem;
}

.avatar-module__fallback-medium-small___E-blx {
  font-size: 1.25rem;
  height: 3.5rem;
  width: 3.5rem;
}

.avatar-module__fallbackBorder___UCUbz {
  border: 1px solid var(--color-primary-white);
}

.avatar-module__initials___dUWaf {
  margin: 0;
  user-select: none;
}

.avatar-module__initials--x-small___IjWaY {
  font-size: var(--fontsize-body-xsmall);
  font-weight: 600;
}

.avatar-module__initials--small___R-w3j {
  font-size: var(--fontsize-body-small);
  font-weight: 600;
}

.avatar-module__initials--large___jocha {
  font-size: var(--fontsize-h6);
}

.avatar-module__initials--bold___JXaq0 {
  font-weight: 600;
}

@media (min-width: 769px) {
  .avatar-module__fallback___sXkqT {
    width: 4rem;
    height: 4rem;
  }

  .avatar-module__fallback--chip___Ix5MA {
    width: 1.5rem;
    height: 1.5rem;
  }

  .avatar-module__fallback--small___Bn-Rc {
    width: 2rem;
    height: 2rem;
  }

  .avatar-module__fallback--small-medium___xNJIF {
    font-size: 1.25rem;
    height: 2.5rem;
    width: 2.5rem;
  }

  .avatar-module__fallback--medium-small___yHhIC {
    height: 3.5rem;
    width: 3.5rem;
  }
}

.accordion-module__accordion___Na9ur {
  width: 100%;
  min-width: 22rem;
  display: flex;
  flex-direction: column;

  .accordion-module__accordionHeader___r3t6m {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 1rem;
    gap: 1rem;
    border-top: 1px solid var(--color-primary-pale-charcoal);
    border-bottom: 1px solid var(--color-primary-pale-charcoal);
    cursor: pointer;
    border-radius: 0;

    .accordion-module__title___F-2Fk {
      font-size: var(--fontsize-body-small);
      font-weight: 600;
      line-height: var(--lineheight-body);
      color: var(--color-primary-charcoal);
    }

    .accordion-module__subtitle___xWoSl {
      color: var(--color-primary-charcoal);
      font-size: 0.875rem;
    }
  }

  .accordion-module__accordionHeader___r3t6m:hover {
    background-color: #f5f5f5;
  }

  .accordion-module__chevronWrapper___8NNVf {
    svg {
      path {
        fill: var(--color-primary-slate);
      }
    }
  }

  [data-scope="accordion"][data-part="chevron"][data-state="open"] {
    transform: rotate(90deg);
    transition: transform 100ms ease-in-out;
  }

  [data-scope="accordion"][data-part="chevron"][data-state="closed"] {
    transition: transform 100ms ease-in-out;
  }

  [data-scope="accordion"][data-part="content"] {
    background-color: var(--color-bg-white);
    border-top: 1px solid var(--color-primary-pale-charcoal);
    border-bottom: 1px solid var(--color-primary-pale-charcoal);
    border-top: none;
    padding: 1rem 1rem 1rem 3rem;
  }

  [data-scope="accordion"][data-part="content"][data-state="open"] {
    opacity: 1;
    display: flex;
    visibility: visible;
    transition: all 100ms ease-in-out;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 1rem;
    align-self: stretch;
  }

  [data-scope="accordion"][data-part="content"][data-state="closed"] {
    opacity: 0;
    height: 0;
    padding: 0 1rem 0 3rem;
    visibility: hidden;
    transition: all 100ms ease-in-out;
  }
}

.assigneeTooltip-module__tooltip___NxT7N {
  background-color: var(--color-primary-charcoal);
  color: var(--color-primary-pale-charcoal);
  border-radius: 3px;
  font-size: var(--fontsize-body);
  font-family: "ProximaNova", Arial, sans-serif;
  line-height: var(--lineheight-body-small);
  padding: 16px;
  max-width: 235px;
  z-index: var(--z-index-tooltip);
  white-space: pre-line;
}

.assigneeTooltip-module__smallSize___Bj-qG {
  padding: 4px 8px;
  font-size: var(--fontsize-body-small);
}

.assigneeTooltip-module__wide___Tc8XE {
  max-width: 280px;
}

.assigneeTooltip-module__arrow___YD-EX {
  --arrow-size: 9px;
  --arrow-background: var(--color-primary-charcoal);
}

.input-module__inputWrapper___QwJwa {
  width: 100%;
  position: relative;
  margin-bottom: 1rem;
}

.input-module__inputField___BsUEA {
  background-color: var(--color-bg-white);
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 2px;
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body);
  font-family: inherit;
  line-height: var(--lineheight-body);
  height: 48px;
  box-sizing: border-box;
  transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.input-module__inputField___BsUEA:read-only {
  cursor: pointer;
}

.input-module__inputThin___As51m {
  height: auto;
  padding: 0.5rem;
}

.input-module__searchTerm___oOCqr {
  border: 1px solid var(--color-primary-charcoal);
}

.input-module__inputField___BsUEA:disabled {
  background-color: var(--color-bg-light-grey);
  border-color: var(--color-border-pale-grey);
  color: var(--color-bg-inactive-charcoal);
  cursor: not-allowed;
}

.input-module__baseAlign___lyZiR {
  padding: 1rem;
  padding-top: 2.25rem;
}

.input-module__centerAlign___yOfNA {
  padding: 1.625rem 1rem;
}

.input-module__inputField___BsUEA:focus {
  outline: none;
  border: 1px solid var(--color-primary-charcoal);

  &.focusedByKeyboard {
    border: 1px solid var(--color-border-pale-grey);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }
}

.input-module__disappearingLabel___-SjiJ,
.input-module__floatingLabel___z4Wsn {
  position: absolute;
  top: 1rem;
  left: 1rem;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-bg-inactive-charcoal);
  pointer-events: none;
  z-index: 1;
}

.input-module__floatingLabel___z4Wsn {
  transition: 0.2s ease-in-out;
}

.input-module__disappearingLabel___-SjiJ {
  visibility: visible;
  opacity: 1;
  transition: opacity 2s linear;
  transform: translate(15px, -10px);
}

.input-module__floatingLabelActive___KWdqs {
  top: 8px;
  font-size: var(--fontsize-body-xsmall);
  line-height: var(--lineheight-body-xsmall);
  color: var(--color-primary-light-charcoal);
}

.input-module__inputWrapper___QwJwa:focus-within .input-module__floatingLabel___z4Wsn,
.input-module__inputWrapper___QwJwa input:not(:placeholder-shown) + .input-module__floatingLabel___z4Wsn {
  top: 8px;
  font-size: var(--fontsize-body-xsmall);
  line-height: var(--lineheight-body-xsmall);
}

.input-module__hidden___GaZcX {
  display: none;
}

.input-module__inputError___OUDEA {
  border: 1px solid var(--color-primary-red);
}

.input-module__errorTextContainer___-Kvch {
  display: flex;
  gap: 0.5rem;
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  margin-top: 0.75rem;
}

.input-module__additionalErrorMessage___Olqhp {
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
}

.input-module__errorText___jT5Un {
  margin: 0;
  line-height: 1;
  text-align: left;
  font-size: var(--fontsize-body-small);
}

.input-module__iconWrapper___OEOi8 {
  position: absolute;
  color: var(--color-primary-slate);
  top: 16px;
  left: 10px;
  transition: opacity 2s linear;
}

.input-module__requiredFieldsTextAsterisk___L44F- {
  font-size: var(--fontsize-body);
  color: var(--color-primary-red);
}

.input-module__searchIconWrapper___iJhnm {
  position: absolute;
  right: 25px;
  top: 14px;

  svg {
    path {
      fill: var(--color-primary-charcoal);
    }
  }
}

@media (min-width: 769px) {
  .input-module__inputWrapper___QwJwa {
    margin-bottom: 0;
  }
}

.autocomplete-module__autoCompleteWrapper___QFJ8Y {
  position: relative;
  width: 90%;
}

.autocomplete-module__autoCompleteInputWrapper___nqwaY {
  width: 100%;
  position: relative;
}

.autocomplete-module__autoCompleteInput___MX0lu {
  background-color: var(--color-bg-white);
  width: 100%;
  padding: 1rem;
  padding-top: 2.25rem;
  border: 1px solid var(--color-border-grey);
  border-radius: 2px;
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  height: 48px;
  box-sizing: border-box;
  transition: border-color 0.2s ease-in-out;
}

.autocomplete-module__autoCompleteInput___MX0lu:focus-visible {
  outline: none;
  border: 1px solid var(--color-primary-charcoal);
}

.autocomplete-module__floatingLabel___q-frp {
  position: absolute;
  top: 50%;
  left: 1rem;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-bg-inactive-charcoal);
  transform: translateY(-50%);
  pointer-events: none;
  transition: 0.2s ease-in-out;
  z-index: 1;
}

.autocomplete-module__floatingLabelActive___dEPCh {
  top: 15px;
  font-size: var(--fontsize-body-xsmall);
  line-height: var(--lineheight-body-xsmall);
  color: var(--color-primary-charcoal);
}

.autocomplete-module__autoCompleteInputWrapper___nqwaY:focus-within .autocomplete-module__floatingLabel___q-frp,
.autocomplete-module__autoCompleteInputWrapper___nqwaY input:not(:placeholder-shown) + .autocomplete-module__floatingLabel___q-frp {
  top: 15px;
  font-size: var(--fontsize-body-xsmall);
  line-height: var(--lineheight-body-xsmall);
  color: var(--color-primary-charcoal);
}

.autocomplete-module__autoCompleteDropdown___fcmlR {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  width: 100%;
  max-width: 100%;
  padding: 1rem;
  background-color: var(--color-bg-white);
  border: 1px solid var(--color-border-grey);
  border-radius: 2px;
  color: black;
  box-sizing: border-box;
}

.autocomplete-module__checkMark___1iTW3 {
  display: flex;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.avatarCard-module__avatarCard___z1N8s {
  align-items: center;
  display: flex;
  background-color: var(--color-bg-white);
  border-radius: 8px;
  gap: 32px;
  width: 100%;
  max-width: 574px;
  max-height: 100px;
  color:var(--color-primary-charcoal);
}

.avatarCard-module__name___kwQUM {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 600;
  margin: 0;
  margin-bottom: 0.5rem;
}

.avatarCard-module__cardInfo___MBtbd p {
  margin: 0;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
}

.avatarCard-module__cardInfoItem___zYxGN {
  align-items: center;
  padding-bottom: 0.25rem;
  display: flex;
  gap: 0.5rem;
}

@media (min-width: 1025px) {
  .avatarCard-module__avatarCard___z1N8s {
    padding: 1.5rem;
    box-shadow: 0px 4px 24px 0px #00000026;
    max-height: 136px;
  }

  .avatarCard-module__name___kwQUM {
    margin-bottom: 0.5rem;
  }
}

.buttonDropdown-module__buttonDropdownWrapper___mZIKF {
  position: relative;
  z-index: var(--z-index-btn-dropdown);

  [data-scope="menu"][data-part="trigger"] {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 2px;
    padding: 0.5rem 1rem;
    color: var(--color-primary-charcoal);
    line-height: var(--lineheight-body);
    border: 1px solid var(--color-primary-charcoal);
    font-weight: 600;
    background-color: var(--color-primary-soft-charcoal);
    transition: 0.2s background-color;
    outline: none;

    svg {
      path {
        fill: var(--color-primary-charcoal);
      }
    }
  }

  [data-scope="menu"][data-part="trigger"]:hover {
    color: var(--color-bg-white);
    background-color: var(--color-primary-charcoal);
    cursor: pointer;

    svg {
      path {
        fill: var(--color-bg-white);
      }
    }
  }

  [data-scope="menu"][data-part="trigger"]:focus-visible {
    margin: 0;
    border: 1px solid var(--color-primary-charcoal);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }

  [data-scope="menu"][data-part="trigger"].buttonDropdown-module__tertiaryTrigger___uED9A {
    color: var(--color-primary-charcoal);
    border: 1px solid var(--color-primary-charcoal);
    background-color: var(--color-primary-soft-charcoal);

    svg path {
      fill: var(--color-primary-charcoal);
    }
  }

  [data-scope="menu"][data-part="trigger"].buttonDropdown-module__tertiaryTrigger___uED9A:hover {
    color: var(--color-bg-white);
    background-color: var(--color-primary-charcoal);

    svg path {
      fill: var(--color-bg-white);
    }
  }

  [data-scope="menu"][data-part="trigger"].buttonDropdown-module__tertiaryTrigger___uED9A:focus-visible {
    margin: 0;
    border: 1px solid var(--color-primary-charcoal);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }

  [data-scope="menu"][data-part="trigger"].buttonDropdown-module__primaryTrigger___nd6kn {
    color: var(--color-bg-white);
    border: 1px solid var(--color-primary-red);
    background-color: var(--color-primary-red);

    svg path {
      fill: var(--color-bg-white);
    }
  }

  [data-scope="menu"][data-part="trigger"].buttonDropdown-module__primaryTrigger___nd6kn:hover {
    color: var(--color-bg-white);
    background-color: var(--color-secondary-burgundy);
    border: 1px solid var(--color-secondary-burgundy);
    svg path {
      fill: var(--color-bg-white);
    }
  }

  [data-scope="menu"][data-part="trigger"].buttonDropdown-module__primaryTrigger___nd6kn:focus-visible {
    margin: 0;
    border: 1px solid var(--color-secondary-burgundy);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }

  [data-scope="menu"][data-part="content"] {
    top: calc(100% + 0.25rem);
    right: 0;
    border: 0;
    width: 15rem;
    padding: 0;
  }

  [data-scope="menu"][data-part="item"] {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    padding: 1rem;
    gap: 1rem;
    color: var(--color-primary-charcoal);
    cursor: pointer;
    transition: background-color 300ms linear;
    text-wrap-mode: nowrap;
    height: auto;
    width: auto;

    svg {
      path {
        fill: var(--color-bg-white);
      }
    }
  }

  .buttonDropdown-module__charcoalIconItem___THHg4 {
    svg {
      path {
        fill: var(--color-primary-charcoal) !important;
      }
    }
  }

  [data-scope="menu"][data-part="item"]:hover {
    background-color: var(--color-primary-pale-charcoal);
  }
}

.taskType-module__taskType___EbV6J {
  display: grid;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  color: var(--color-primary-white);
  width: 1.25rem;
  min-width: 1.25rem;
  height: 1.25rem;
}

.taskType-module__burgundy___zs7G- {
  background-color: var(--color-secondary-burgundy);
}

.taskType-module__emerald___Onlnd {
  background-color: var(--color-secondary-emerald);
}

.taskType-module__ocean___tT9LA {
  background-color: var(--color-secondary-ocean);
}

.taskType-module__gold___BE0zn {
  background-color: var(--color-secondary-gold);
}

.taskType-module__jade___xnVx2 {
  background-color: var(--color-secondary-jade);
}

.selector-module__selector___PBMIP {
  display: flex;
  flex-direction: column;
}

.selector-module__selectorWrapperRegularRadio___DQVqz,
.selector-module__selectorWrapper___mZCi5 {
  background-color: var(--color-bg-white);
  width: 100%;
}

.selector-module__selectorWrapper___mZCi5 {
  border: 1px solid var(--color-border-grey);
  border-radius: 2px;
  padding: 1rem;
  min-height: 104px;
}

.selector-module__selectorWrapperRegularRadio___DQVqz {
  padding-bottom: 1.5rem;
  padding-right: 1rem;
}

.selector-module__selectorWrapperRegularRadio___DQVqz:last-child {
  padding-bottom: 0px;
}

.selector-module__selectorWrapper___mZCi5:focus-within {
  &.focusedByKeyboard {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }
}
.selector-module__selectorWrapperActive___r3W2M {
  border-color: var(--color-border-primary-charcoal);
}

.selector-module__selectorSelect___I3o52 {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.selector-module__selectorLabel___AoHR1 {
  display: none;
}

.selector-module__selectorItem___WfmuZ {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  height: 100%;
  cursor: pointer;

  &:hover {
    .selector-module__selectorInput___4OQNY {
      border-color: var(--color-primary-charcoal);
    }
  }
}

.selector-module__selectorControlWrapper___la-EU {
  display: flex;
  width: 1.5rem;
  justify-content: center;
}

.selector-module__selectorTextRegularRadio___EZI7K,
.selector-module__selectorText___hYjll {
  display: flex;
  gap: 0.5rem;
  height: 100%;
  flex: 2;
  color: var(--color-primary-charcoal);
}

.selector-module__selectorText___hYjll {
  flex-direction: column;
  align-items: flex-start;
}

.selector-module__selectorTextRegularRadio___EZI7K {
  align-items: center;
}

.selector-module__title___kYBGF {
  margin: 0;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body-small);
}

.selector-module__description___ZHQAP {
  margin: 0;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  text-align: left;
  color: var(--color-primary-light-charcoal);
}

.selector-module__selectorWrapperActive___r3W2M .selector-module__selectorText___hYjll .selector-module__title___kYBGF {
  font-weight: 600;
}

.selector-module__selectorInput___4OQNY {
  height: 1.5rem;
  width: 1.5rem;
  border: 1px solid var(--color-border-grey);
  border-radius: 50%;
  position: relative;
  transition: outline-offset 0.3s ease, outline-width 0.3s ease,
    height 0.3s ease, width 0.3s ease;

  &.focusedByKeyboard {
    &[data-focus] {
      &:before {
        content: "";
        outline: 2px solid var(--color-secondary-cobalt);
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        border-radius: 50%;
        outline-offset: 1px;
      }
    }
  }
}

.selector-module__selectorInput___4OQNY:is(:checked, [data-checked], [aria-checked="true"], [data-state="checked"]) {
  height: 0.75rem;
  width: 0.75rem;
  outline-offset: 3px;
  outline-width: 2px;
  background: var(--color-border-primary-charcoal);
  border-color: var(--color-border-primary-charcoal);
  outline-color: var(--color-border-primary-charcoal);
  outline-style: solid;

  &[data-focus] {
    &:before {
      outline-offset: 0.5rem;
    }
  }
}

@media (min-width: 769px) {
  .selector-module__selectorWrapper___mZCi5 {
    flex-basis: calc(50% - 10px);
    max-width: calc(50% - 10px);
  }
}

.cardSelector-module__cardSelectorRegularRadio___UtDFG,
.cardSelector-module__cardSelector___NsCZE {
  border-radius: 0;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.cardSelector-module__cardSelector___NsCZE {
  gap: 1.25rem;
}

.carousel-module__carousel___ealh- {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;
  width: 100%;
  position: relative;
  gap: 0.5rem;
}

.carousel-module__carousel-viewport___JD9Sy {
  position: relative;
}

.carousel-module__carousel-img___rba07 {
  width: 100%;
  max-height: 550px;
  object-fit: contain;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
  position: relative;
  opacity: 0;
  transition: opacity 0.5s ease-in-out, transform 0.25s ease-in-out;
  object-fit: contain;
  overflow: hidden;
}

.carousel-module__carousel-item-active___y0w-U .carousel-module__carousel-img___rba07 {
  opacity: 1;
  transform: none;
}

.carousel-module__carousel-controls___pdqjL {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding-right: 1rem;
  position: relative;
  z-index: 2;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.carousel-module__carousel-buttons___W5ILr {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.carousel-module__carouselButton___gw5uI {
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 10px;
  outline: none;
  color: var(--color-bg-white);
}

.carousel-module__carouselButtonControlColors___cp7k6 {
  border-radius: 50%;
  background-color: var(--color-bg-primary-charcoal);
}

.carousel-module__carouselButton___gw5uI:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

.carousel-module__carouselButtonControlColors___cp7k6:hover {
  border-radius: 50%;
  background-color: var(--color-bg-primary-charcoal);
}

.carousel-module__carouselButton___gw5uI:focus-visible {
  outline: none;
  box-shadow: none;
}

.carousel-module__pausePlayButton___qVisS {
  display: flex;
  justify-content: center;
  background-color: transparent;
  z-index: 2;
}

.carousel-module__pausePlayButton___qVisS button {
  background-color: transparent;
  padding: 10px;
  border: none;
  cursor: pointer;
  outline: none;
}

.carousel-module__pausePlayButton___qVisS button:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

.carousel-module__pausePlayButton___qVisS button:focus-visible {
  outline: none;
  box-shadow: none;
}

.carousel-module__pausePlayButtonControlColors___otr-9 button,
.carousel-module__pausePlayButtonControlColors___otr-9:hover,
.carousel-module__carouselButton___gw5uI:focus-visible,
.carousel-module__pausePlayButton___qVisS button:focus-visible {
  background-color: var(--color-bg-primary-charcoal);
  border-radius: 50%;
}

.carousel-module__carousel-item-group___2tOEr {
  display: flex;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.carousel-module__carousel-item___fnwSS {
  flex: 0 0 100%;
  position: relative;
  opacity: 0;
  transition: opacity 0.25s ease-in-out, transform 0.25s ease-in-out;
}

.carousel-module__carousel-item-active___y0w-U {
  opacity: 1;
  transform: none;
}

.carousel-module__carousel-text___R-srU {
  width: 65%;
  color: var(--color-bg-white);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  flex-shrink: 1;
  justify-self: flex-start;
}

@media (min-width: 769px) {
  .carousel-module__carousel-img___rba07 {
    max-height: 800px;
  }

  .carousel-module__carousel-controls___pdqjL {
    flex-direction: row;
    margin-top: 0;
  }

  .carousel-module__carousel-buttons___W5ILr {
    margin-top: 0;
  }
}

.checkbox-module__checkboxWrapper___2fFXa {
  position: relative;
  display: flex;
  flex-direction: row;
  gap: 0.75rem;
  align-items: center;
  justify-content: center;

  &:focus-within {
    outline: none;

    &.focusedByKeyboard {
      .checkbox-module__checkbox___FLuy1 {
        outline: 2px solid var(--color-secondary-cobalt);
        outline-offset: -2px;
        border-radius: 2px;
      }
    }
  }
}

.checkbox-module__checkbox___FLuy1 {
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.checkbox-module__checkbox___FLuy1.checkbox-module__large___EpVxK {
  height: 1.5rem;
  width: 1.5rem;
}

.checkbox-module__checkbox___FLuy1.checkbox-module__small___K75Km {
  height: 1.25rem;
  width: 1.25rem;
}

.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu {
  background-color: var(--color-border-light-grey) !important;
}

.checkbox-module__checkbox___FLuy1[data-state="unchecked"] {
  background-color: var(--color-bg-white);
  border: 1.5px solid var(--color-border-grey);
  margin: 0;
  transition: background-color 100ms linear;

  svg {
    display: none;
  }
}

.checkbox-module__checkbox___FLuy1[data-state="unchecked"]:focus-visible {
  border: 1.5px solid var(--color-secondary-cobalt);
}

.checkbox-module__checkbox___FLuy1[data-state="checked"] {
  background-color: var(--color-primary-charcoal);
  border: 1.5px solid var(--color-primary-charcoal);
  transition: background-color 100ms linear;
}

.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu[data-state="checked"] {
  border: 1.5px solid var(--color-border-grey);
  background-color: var(--color-border-grey) !important;
}

.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu[data-state="checked"]:hover,
.checkbox-module__checkbox___FLuy1.checkbox-module__disabled___zhmLu[data-state="unchecked"]:hover {
  border: 1.5px solid var(--color-border-grey) !important;
}

.checkbox-module__checkbox___FLuy1:hover {
  border: 1.5px solid var(--color-primary-charcoal) !important;
}

.checkbox-module__checkmark___Gafvb {
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.checkbox-module__checkboxLabel___lyYL5 {
  font-weight: 400;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-primary-charcoal);
}

.checkbox-module__checkboxLabel___lyYL5.checkbox-module__disabled___zhmLu {
  color: var(--color-bg-inactive-charcoal);
}

@media (min-width: 768px) {
  .checkbox-module__checkboxLabel___lyYL5 {
    font-size: var(--fontsize-default);
  }
}

.checkboxCard-module__checkboxCard___6-MvT {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: var(--color-bg-white);
  border-radius: 2px;
  border: 1px solid var(--color-border-grey);
  padding: 1rem;
  width: 100%;
  color: var(--color-primary-charcoal);

  .checkboxCard-module__labelWrapper___6Ed27 {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 1rem;

    svg {
      margin-right: 1rem;
    }
  }

  .checkboxCard-module__checkbox___KZ8ke {
    width: 1.5rem;
  }

  [data-scope="checkbox"][data-part="control"]:hover {
    border: 1.5px solid var(--color-border-grey);
  }

  [data-scope="checkbox"][data-part="label"] {
    display: none;
  }

  &:focus-within {
    outline: 3px solid var(--color-secondary-cobalt);
  }
}

.checkboxCard-module__checkboxCard___6-MvT[data-state="unchecked"]:focus-visible::before {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  left: -1.5px;
  top: -1.5px;
  border: 1.5px solid var(--color-secondary-cobalt);
}

.checkboxCard-module__checkboxCard___6-MvT[data-state="unchecked"]:focus-visible {
  border: 3px solid var(--color-outline-dark-bg);
}

.checkboxCard-module__checkboxCard___6-MvT[data-scope="checkbox-card"][data-state="checked"],
.checkboxCard-module__checkboxCard___6-MvT:hover,
[data-scope="checkbox"][data-part="control"][data-state="checked"]:hover {
  border: 1px solid var(--color-primary-charcoal);
}

.checkboxCard-module__checkboxCard___6-MvT[data-scope="checkbox-card"][data-state="checked"][data-disabled="active"] {
  .checkboxCard-module__labelWrapper___6Ed27 {
    font-weight: bold;
  }
}

.checkboxCard-module__checkboxCard___6-MvT[data-scope="checkbox-card"][data-disabled="disabled"] {
  border: 1px solid var(--color-border-grey);
  background: var(--color-bg-light-grey);
  color: var(--color-bg-inactive-charcoal);

  [data-part="indicator"]:hover {
    cursor: not-allowed;
  }

  .checkboxCard-module__labelWrapper___6Ed27 {
    svg {
      path {
        fill: var(--color-bg-inactive-charcoal);
      }
    }
  }
}

.chipDropdown-module__chipDropdown___9z3S- {
  position: relative;
  z-index: var(--z-index-dropdown-list);

  .chipDropdown-module__trigger___mbdgb {
    display: flex;
    flex-direction: row;
    padding: 0.5rem 1rem;
    align-items: center;
    gap: 0.5rem;
    border-radius: 6.25rem;
    border: 1px solid var(--color-primary-pale-charcoal);
    background: var(--color-bg-white);
    cursor: pointer;
    transition: border 100ms linear;
    color: var(--color-primary-light-charcoal);

    .chipDropdown-module__chipDownLabel___iz9II {
      font-size: var(--fontsize-body-small);
      line-height: var(--lineheight-body-small);
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }

    .chipDropdown-module__chipDownLabelDivider___vqDBb {
      width: 1px;
      height: 1rem;
      background-color: var(--color-primary-pale-charcoal);
    }

    .chipDropdown-module__triggerChevronWrapper___H7EmK,
    .chipDropdown-module__triggerIconWrapper___1vlGN {
      height: 1rem;
    }

    .chipDropdown-module__triggerIconWrapper___1vlGN {
      svg {
        path {
          fill: var(--color-primary-slate);
        }
      }
    }

    &[data-state="closed"] {
      .chipDropdown-module__triggerChevronWrapper___H7EmK {
        svg {
          path {
            fill: var(--color-primary-slate);
          }
        }
      }
    }

    &[data-state="open"] {
      .chipDropdown-module__triggerChevronWrapper___H7EmK {
        transform: rotate(180deg);
        svg {
          path {
            fill: var(--color-primary-charcoal);
          }
        }
      }
    }
  }

  .chipDropdown-module__trigger___mbdgb[data-state="open"] {
    border: 1px solid var(--color-primary-charcoal);
    transition: border 100ms linear;
    outline: none;
  }

  .chipDropdown-module__trigger___mbdgb:focus-visible {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }

  .chipDropdown-module__searchBarWrapper___fUdsF {
    display: flex;
    margin: 1rem 1rem 0rem 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    align-self: stretch;
    position: relative;
  }

  .chipDropdown-module__searchBar___N3J1B {
    display: flex;
    padding: 0.75rem;
    padding-left: 2rem;
    align-items: center;
    gap: 0.25rem;
    align-self: stretch;
    border: 1px solid var(--color-primary-pale-charcoal);
    border-radius: 100px;
    background: var(--color-bg-white);
    font-size: 0.875rem;
    position: relative;

    &:hover {
      border-color: var(--color-primary-charcoal);
    }

    &:focus-visible {
      outline: none;
      border: 1px solid var(--color-primary-charcoal);

      &.focusedByKeyboard {
        outline: 2px solid var(--color-outline-dark-bg);
      }
    }
  }

  .chipDropdown-module__searchBarIconWrapper___cgQ1h {
    position: absolute;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 200;
    color: var(--color-primary-slate);
    pointer-events: none;
  }

  .chipDropdown-module__searchBar___N3J1B::placeholder {
    color: var(--color-bg-inactive-charcoal);
    line-height: var(--lineheight-h6);
  }

  [data-scope="popover"][data-part="content"] {
    border: none;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
    min-width: 17.5rem;
    max-width: 37.5rem;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 0.125rem;
    background-color: var(--color-bg-white);
    padding: 0;
    max-height: 300px;
    overflow-y: auto;
  }

  .chipDropdown-module__resetFilter___Zim5w {
    border-top: 1px solid var(--color-primary-pale-charcoal);
    width: 100%;
    padding: 1rem;
    color: var(--color-bg-inactive-charcoal);
    cursor: pointer;
    border-radius: 0;
    width: 100%;
    position: sticky;
    bottom: 0;
    background-color: var(--color-bg-white);

    &:focus-visible {
      outline-offset: -2px;
    }
  }
}

.chipDropdown-module__trigger___mbdgb:hover {
  background-color: #f2f2f2;
  border-color: var(--color-primary-charcoal);
}

.chipDropdown-module__dropDownItemsContainer___x3AZN {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
  max-width: 100%;

  label {
    width: 100%;
    justify-content: flex-start;
    cursor: pointer;
  }
}

.checkboxDropdownItem-module__checkboxDropdownItem___IsTt6 {
  display: flex;
  padding: 0.25rem 1rem;
  height: 3rem;
  align-items: center;
  align-self: stretch;
  gap: 1rem;
  cursor: pointer;
}

.checkboxDropdownItem-module__avatarAndLabel___oSyzf {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  align-items: center;
  overflow: hidden;
}

.checkboxDropdownItem-module__label___GX61T {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.radioDropdownItem-module__radioDropdownItem___ra9Bk {
  display: flex;
  padding: 0.25rem 1rem;
  height: 3rem;
  align-items: center;
  align-self: stretch;
  gap: 1rem;
}
.circlebutton-module__circleButton___4tntF {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--color-bg-white);
  padding: 0;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.circlebutton-module__circleButtonSmall___zIdlT {
  width: 2rem;
  height: 2rem;
}

.circlebutton-module__circleButtonWithBorder___HPB-E {
  border: 1px solid var(--color-border-grey);
}

.circlebutton-module__circleButton___4tntF:hover,
.circlebutton-module__circleButton___4tntF:focus-visible {
  background-color: var(--color-bg-light-grey);
}

.circlebutton-module__circleButtonEnabled___h4xdJ {
  background-color: var(--color-primary-red);

  svg {
    color: var(--color-bg-white);
  }
}

.circlebutton-module__circleButtonDisabled___kSbIc {
  cursor: default;
}

.circlebutton-module__circleButtonDisabled___kSbIc:hover {
  background-color: var(--color-bg-white);
}

.tooltip-module__tooltip___3gdiI {
  background-color: var(--color-primary-charcoal);
  color: var(--color-primary-pale-charcoal);
  border-radius: 3px;
  font-size: var(--fontsize-body);
  font-family: "ProximaNova", Arial, sans-serif;
  line-height: var(--lineheight-body-small);
  padding: 16px;
  max-width: 235px;
  z-index: var(--z-index-tooltip);
  white-space: pre-line;
}

.tooltip-module__smallSize___pfMRI {
  padding: 4px 8px;
  font-size: var(--fontsize-body-small);
}

.tooltip-module__wide___ozcpb {
  max-width: 280px;
}

.tooltip-module__arrow___CBW5f {
  --arrow-size: 9px;
  --arrow-background: var(--color-primary-charcoal);
}

.tooltip-module__closerToDrawerButton___cOwdf [data-scope="tooltip"] {
  --y: 42px !important;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-regular-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-regular-webfont.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-extrabold-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-extrabold-webfont.woff") format("woff");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-bold-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-bold-webfont.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-semibold-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-semibold-webfont.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-light-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-light-webfont.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-thin-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-thin-webfont.woff") format("woff");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-thinit-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-thinit-webfont.woff") format("woff");
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-boldit-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-boldit-webfont.woff") format("woff");
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "ProximaNova";
  src: url("/fonts/proximanova-regularit-webfont.woff2") format("woff2"),
    url("/fonts/proximanova-regularit-webfont.woff") format("woff");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

:root {
  /* Header Font Sizes */
  --fontsize-h1: 2.25rem;
  --fontsize-h2: 1.875rem;
  --fontsize-h3: 1.625rem;
  --fontsize-h4: 1.375rem;
  --fontsize-h5: 1.25rem;
  --fontsize-h6: 1.125rem;

  /* Body Font Sizes */
  --fontsize-body: 1rem;
  --fontsize-body-small: 0.875rem;
  --fontsize-body-xsmall: 0.75rem;
  --fontsize-body-badge: 0.75rem;
  --fontsize-body-label: 0.75rem;
  --fontsize-body-eyebrow: 0.625rem;
  --fontsize-default: 1rem;

  /* Header Line Heights */
  --lineheight-h1: 3rem;
  --lineheight-h2: 2.5rem;
  --lineheight-h3: 2rem;
  --lineheight-h4: 2rem;
  --lineheight-h5: 1.5rem;
  --lineheight-h6: 1.5rem;

  /* Body Line Heights */
  --lineheight-body: 1.5rem;
  --lineheight-body-small: 1.25rem;
  --lineheight-body-xsmall: 1rem;
  --lineheight-body-badge: 1rem;
  --lineheight-body-label: 1rem;
  --lineheight-body-eyebrow: 1.125rem;
  --lineheight-default: 1.5rem;

  /* Primary Colors */
  --color-primary-red: #e81a3b;
  --color-primary-red-transparent: #e81a3b14;
  --color-primary-red-disabled: #e81a3b4d;
  --color-primary-charcoal: #333333;
  --color-primary-pale-charcoal: #e7e7e7;
  --color-primary-light-charcoal: #404040;
  --color-primary-slate: #5b6e7f;
  --color-primary-white: #ffffff;
  --color-primary-inactive-charcoal: #666666;
  --color-primary-soft-charcoal: #fafafa;

  /* Secondary Colors */
  --color-secondary-burgundy: #98002e;
  --color-secondary-cobalt: #0062b8;
  --color-secondary-cobalt-transparent: #0062b814;
  --color-secondary-emerald: #218f8b;
  --color-secondary-ocean: #008fd2;
  --color-secondary-gold: #d67900;
  --color-secondary-gold-transparent: #d679001a;
  --color-secondary-jade: #009966;

  /* Badge Colors */
  --color-secondary-emerald-badge: #218f8b33;
  --color-secondary-gold-badge: #d6790033;
  --color-secondary-burgundy-badge: #98002e33;

  /* Background Colors */
  --color-bg-white: #ffffff;
  --color-bg-primary-charcoal: var(--color-primary-charcoal);
  --color-bg-primary-pale-charcoal: var(--color-primary-pale-charcoal);
  --color-bg-secondary-cobalt: var(--color-secondary-cobalt);
  --color-bg-grey: #cccfd2;
  --color-bg-light-grey: #f2f2f2;
  --color-bg-light-charcoal: var(--color-primary-light-charcoal);
  --color-bg-inactive-charcoal: #666666;
  --color-bg-light-emerald: #e9f4f3;
  --color-bg-light-cobalt: #e9f1f7;
  --color-bg-light-burgundy: #f7ebee;
  --color-bg-light-cobalt-translucent: #f2f7fb;
  --color-bg-warning-yellow: #f3d429;
  --color-bg-warning-yellow-translucent: #f3d42914;

  /* Border Colors */
  --color-border-pale-grey: var(--color-primary-pale-charcoal);
  --color-border-primary-charcoal: var(--color-primary-charcoal);
  --color-border-light-charcoal: var(--color-primary-light-charcoal);
  --color-border-grey: var(--color-bg-grey);
  --color-border-light-grey: var(--color-bg-light-grey);
  --color-border-white: var(--color-bg-white);
  --color-outline-dark-bg: #cce0f1;
  --z-index-dropdown-list-in-table: 50;
  --z-index-table-header: 80;
  --z-index-dropdown-list: 100;
  --z-index-modal: 700;
  --z-index-dropdown-menu: 650;
  --z-index-drawer: 600;
  --z-index-drawer-overlay: 500;
  --z-index-toast: 800;
  --z-index-spinner: 800;
  --z-index-chip-dropdown: 1000;
  --z-index-btn-dropdown: 200;
  --z-index-popup: 300;
  --z-index-tooltip: 300;

  /* Breakpoints - min-widths */
  --screensize-xxs: 320px; /* mobile devices */
  --screensize-xs: 481px; /* mobile devices, tablet devices */
  --screensize-s: 769px; /* tablet devices, smaller laptop screens*/
  --screensize-m: 1025px; /* desktops, larger screens */
  --screensize-l: 1201px; /* desktops, larger screens*/
  --screensize-xl: 1441px; /* desktops, larger screens */
  --screensize-xxl: 1601px; /* extra large screens */

  /* Primary font-family */
  --primary-font-family: "ProximaNova", Arial, sans-serif;

  /* Sizes */
  --main-header-height: 3.5rem;
}

@media (min-width: 769px) {
  :root {
    --fontsize-h1: 2.812rem;
    --fontsize-h2: 2.25rem;
    --fontsize-h3: 1.875rem;
    --fontsize-h4: 1.375rem;
    --fontsize-h5: 1.375rem;
    --fontsize-h6: 1.25rem;

    --lineheight-h1: 3.5rem;
    --lineheight-h2: 3rem;
    --lineheight-h3: 2.5rem;
    --lineheight-h4: 2rem;
    --lineheight-h5: 2rem;
    --lineheight-h6: 1.5rem;

    /* Sizes */
    --main-header-height: 3.75rem;
  }
}

body {
  font-family: var(--primary-font-family);
}

h1 {
  font-size: var(--fontsize-h1);
  line-height: var(--lineheight-h1);
  font-weight: 600;
}

h2 {
  font-size: var(--fontsize-h2);
  line-height: var(--lineheight-h2);
  font-weight: 600;
}

h3 {
  font-size: var(--fontsize-h3);
  line-height: var(--lineheight-h3);
  font-weight: 600;
}

h4 {
  font-size: var(--fontsize-h4);
  line-height: var(--lineheight-h4);
  font-weight: 600;
}

h5 {
  font-size: var(--fontsize-h5);
  line-height: var(--lineheight-h5);
  font-weight: 600;
}

h6 {
  font-size: var(--fontsize-h6);
  line-height: var(--lineheight-h6);
  font-weight: 600;
}

p {
  font-size: var(--fontsize-default);
  line-height: var(--lineheight-default);
}

.copyclipboard-module__copyToClipboard___ohnuh {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.copyclipboard-module__copyToClipboardText___mP-dd {
  margin: 0;
}

.copyclipboard-module__copyButton___ERldK {
  display: flex;
}

.copyclipboard-module__copyButton___ERldK {
  border: none;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  background-color: transparent;
}

.copyclipboard-module__copyButton___ERldK:hover {
  background-color: var(--color-bg-light-grey);
}

.datepicker-module__datePickerLabel___cSRsu {
  display: none;
}

.datepicker-module__datePicker___s3de9,
.datepicker-module__datePickerCalendar___tNtJw,
.datepicker-module__datePickerCalendarRow___Y8ISP:nth-child(even),
.datepicker-module__datePickerCalendarDayRow___bnF1b {
  background-color: var(--color-bg-white);
}

.datepicker-module__datePicker___s3de9 {
  font-family: "ProximaNova", Arial, sans-serif;
  box-shadow: 0px 2px 4px 0px #00000026;
}

.datepicker-module__control___Jd0ph button {
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.datepicker-module__datePickerInput___nG3Pj {
  width: 100%;
}

.datepicker-module__datePickerButton___QTu6X {
  cursor: pointer;
  border: none;
  padding: 0;
}

/* Styling added to give fixed height to date picker to stop dropdown switching positions */
[data-scope="date-picker"][data-part="positioner"] {
  display: flex;
  min-height: 25rem;
}

/* Avoid extra space for if date picker pops upwards */
[data-scope="date-picker"][data-part="content"][data-placement="top-start"],
[data-scope="date-picker"][data-part="content"][data-placement="top-end"],
[data-scope="date-picker"][data-part="content"][data-placement="top"] {
  align-self: flex-end;
}

/* Avoid extra space for if date picker pops downwards */
[data-scope="date-picker"][data-part="content"][data-placement="bottom-start"],
[data-scope="date-picker"][data-part="content"][data-placement="bottom-end"],
[data-scope="date-picker"][data-part="content"][data-placement="bottom"] {
  align-self: flex-start;
}

.datepicker-module__datePickerButton___QTu6X svg path {
  fill: var(--color-primary-charcoal);
}

.datepicker-module__datePickerTrigger___-pLav {
  background-color: var(--color-bg-white);
  color: var(--color-primary-charcoal);
  font-weight: 600;
  padding: 0;
  border: none;
}

.datepicker-module__datePickerInput___nG3Pj {
  width: 100%;
  background-color: var(--color-bg-white);
  font-family: "ProximaNova", Arial, sans-serif;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-primary-charcoal);
  padding: 0.5rem 1rem;
  border-radius: 2px;
  border: 1px solid var(--color-border-pale-grey);
  outline: none;

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.datepicker-module__datePickerInput___nG3Pj:focus-visible {
  outline: none;

  &.focusedByKeyboard {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }
}

.datepicker-module__datePickerInput___nG3Pj[data-state="open"] {
  border: 1px solid var(--color-bg-primary-charcoal);
}

.datepicker-module__datePickerControl___J7h-X {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
}

.datepicker-module__datePickerBody___noLH- {
  padding: 0.5rem 1rem 1rem;
}

.datepicker-module__datePickerCalendarDays___bb4ow,
.datepicker-module__datePickerDate___41T2G {
  text-align: center;
  border: none;
  color: var(--color-primary-charcoal);
}

.datepicker-module__datePickerDate___41T2G {
  padding: 0;
  width: 2.5rem;
  height: 2rem;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  border-radius: 2px;
}

.datepicker-module__datePickerDate___41T2G:hover {
  cursor: pointer;
  color: var(--color-primary-charcoal);
  background-color: var(--color-primary-soft-charcoal);
}

.datepicker-module__datePickerCalendarDays___bb4ow {
  font-weight: 400;
  font-size: 0.85rem;
  color: var(--color-bg-inactive-charcoal);
  width: 14.285%; /* 1/7 */
}

.datepicker-module__highlightedDate___bYggA {
  position: relative;
  font-weight: 600;
}

.datepicker-module__hiddenDate___91QCZ {
  visibility: hidden;
}

.datepicker-module__highlightedDate___bYggA::after {
  content: "";
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translate(-50%, 50%);
  width: 4px;
  height: 4px;
  background-color: var(--color-primary-red);
  border-radius: 50%;
}

.datepicker-module__disabledDate___SxXLp,
.datepicker-module__outsideMonthDate___6Pc5Q {
  color: var(--color-bg-grey);
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
}

.datepicker-module__tableCellTrigger___l3vh5 {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

[data-scope="date-picker"][data-part="content"][data-state="open"] {
  z-index: var(--z-index-modal);
  position: relative;
}

.datepicker-module__customRange___DAQjV {
  text-align: center;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body);
  color: var(--color-bg-inactive-charcoal);
}

.datepicker-module__rangeTriggers___Am1lD {
  border-radius: 100px;
  border: 1px solid var(--color-primary-pale-charcoal);
  padding: 0.5rem 1rem;
  background-color: var(--color-bg-white);
  color: var(--color-primary-light-charcoal);
  cursor: pointer;
  font-size: 0.875rem;
}

.datepicker-module__customRanges___6lf6j {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid var(--color-primary-pale-charcoal);
}

.datepicker-module__rangeStart___eFyKb,
.datepicker-module__rangeStart___eFyKb:hover,
.datepicker-module__rangeEnd___MVw-K,
.datepicker-module__rangeEnd___MVw-K:hover,
.datepicker-module__selectedDate___7gQvH,
.datepicker-module__selectedDate___7gQvH:hover {
  background-color: var(--color-primary-red);
  color: var(--color-bg-white);
  border-radius: 2px;
  font-weight: 600;
}

/* Lighter red for the selected range */
.datepicker-module__rangeBetween___ZHeKx,
.datepicker-module__rangeBetween___ZHeKx:hover {
  background-color: var(
    --color-secondary-burgundy-badge
  ); /* Your lighter red */
  color: var(--color-primary-charcoal);
  font-weight: 400;
}

.datepicker-module__clickable___m1-wW,
.datepicker-module__hover___8zdPO:hover,
.datepicker-module__datePickerButton___QTu6X,
.datepicker-module__datePickerCalendarDays___bb4ow,
.datepicker-module__datePickerDate___41T2G:not(.datepicker-module__disabledDate___SxXLp) {
  cursor: pointer;
}

[data-scope="date-picker"][data-part="table"] {
  width: 100%;
}

[data-scope="date-picker"][data-part="table-cell"][aria-disabled="true"] {
  color: var(--color-bg-grey);
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
}

[data-scope="date-picker"][data-part="table-header"] {
  padding: 0.5rem;
}

.datepicker-module__rangeSelected___rDJnD {
  border: 1px solid var(--color-primary-charcoal);
}

.datepicker-module__actions___h-Lbo {
  border-top: 1px solid var(--color-primary-pale-charcoal);
  display: flex;
  justify-content: space-between;
}

.datepicker-module__resetButton___73r1F {
  color: var(--color-bg-inactive-charcoal);
  cursor: pointer;
  padding: 1rem;
  border-radius: 0;
  text-align: left;
}

[data-scope="file-upload"][data-part="root"] {
  display: grid;
  width: 100%;
  height: 100%;
  background-color: var(--color-primary-white);
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 2px;
}

.documentUpload-module__textContainer___JfO4Z {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
}

.documentUpload-module__title___KDbcJ {
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 600;
  margin: 0;
}

.documentUpload-module__titleAsterisk___QkhSZ {
  color: var(--color-secondary-burgundy);
  margin-left: 0.25rem;
}

.documentUpload-module__subtitle___rNX0c {
  color: var(--color-primary-light-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  margin: 0;
}

.documentUpload-module__dropzoneContainer___06wiX {
  padding: 0 1rem 1rem;
}

.documentUpload-module__dropzoneContainerError___k4Z9A {
  [data-scope="file-upload"][data-part="dropzone"] {
    border-color: var(--color-secondary-burgundy);
  }
}

[data-scope="file-upload"][data-part="root"] {
  height: auto;
}

[data-scope="file-upload"][data-part="dropzone"] {
  display: grid;
  justify-items: center;
  align-items: center;
  padding: 1rem 1.5rem;
  border: 1px solid var(--color-bg-primary-pale-charcoal);
  border-style: dashed;
  border-radius: 2px;
  background-color: #fafafa;
  color: var(--color-bg-inactive-charcoal);
  transition: all 0.5s ease;
}

[data-scope="file-upload"][data-part="dropzone"]:hover {
  cursor: pointer;
  background-color: var(--color-bg-light-grey);
}

[data-scope="file-upload"][data-part="dropzone"][data-dragging] {
  background-color: var(--color-bg-light-cobalt-transparent);
  border-color: var(--color-secondary-cobalt);
}

[data-scope="file-upload"][data-part="dropzone"]:hover {
  cursor: pointer;
}

.documentUpload-module__dropdownText___Ce6xu {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 600;
}

.documentUpload-module__dropdownTextBlue___mj5nC {
  color: var(--color-secondary-cobalt);
}

[data-scope="file-upload"][data-part="item-group"] {
  margin: 0;
  padding: 0;
}

[data-scope="file-upload"][data-part="item"] {
  display: grid;
  align-items: center;
  border-top: 1px solid var(--color-border-pale-grey);
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  grid-template-columns: auto 1fr auto auto;
  padding: 0.75rem 1rem;
  column-gap: 0.75rem;
}

[data-scope="file-upload"][data-part="item-name"] {
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body);
  line-height: var(--fontsize-body);
  word-break: break-word;
}

[data-scope="file-upload"][data-part="item-delete-trigger"] {
  border: none;
  outline: none;
  padding: 0.5rem;
  margin: 0;
  width: auto;
  background-color: transparent;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.25s ease-in-out;

  svg {
    color: var(--color-primary-charcoal);
  }
}

[data-scope="file-upload"][data-part="item-delete-trigger"]:hover {
  background-color: var(--color-bg-light-grey);
  cursor: pointer;
}

.documentUpload-module__documentIcon___2YP9M {
  display: grid;
  align-items: center;
  justify-items: center;
  background-color: var(--color-bg-light-grey);
  border-radius: 2px;
  padding: 0.5rem;
  color: var(--color-primary-slate);
}

.documentUpload-module__pendingIcon___I8YaQ {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2rem;
  width: 2rem;
}

.documentUpload-module__nameContainer___zJ9-B {
  font-size: var(--fontsize-body-small);
  line-height: var(--fontsize-body-small);
  color: var(--color-bg-inactive-charcoal);
  justify-self: end;
}

.documentUpload-module__toastWrapper___0hov0 {
  position: fixed;
  transform: translate(-50%, 0);
  bottom: 1rem;
  right: 1.5rem;
  min-width: 358px;
}

.documentUpload-module__noDocumentsContainer___jrAuD {
  padding: 1rem;
  border-top: 1px solid var(--color-bg-primary-pale-charcoal);
}

.documentUpload-module__noDocumentsInfo___jle6j {
  padding: 1rem 1.5rem;
  border-radius: 2px;
  background-color: #fafafa;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-primary-inactive-charcoal);
  text-align: center;
}

.documentUpload-module__errorTextContainer___vFkny {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  padding: 0.5rem 0;
}

.documentUpload-module__errorText___5xgFV {
  margin: 0;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body);
  text-wrap: nowrap;
}

@media (min-width: 768px) {
  .documentUpload-module__toastWrapper___0hov0 {
    bottom: 128px;
    left: default;
    transform: none;
  }

  .documentUpload-module__labels___8KCRg {
    grid-template-columns: 1fr auto;
  }
}

.toaster-module__toaster___ldRvX {
  position: fixed;
  z-index: 9999;
  bottom: 10px;
  right: 16px;
}

.toaster-module__toast___amRbV {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.65rem 1rem;
  border-radius: 2px;
  color: var(--color-primary-charcoal);
  cursor: default;
  width: 100%;
  max-width: 475px;
  min-width: 475px;
  word-break: break-word;
  border: 1px solid;
  box-sizing: border-box;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
  position: relative !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
  opacity: 1;
}

[data-scope="toast"][data-part="group"] {
  position: fixed;
  bottom: 16px;
  right: 36px !important;
  padding: 0.5rem;
  z-index: 10000;
  display: flex;
  flex-direction: column-reverse;
  gap: 16px;
  pointer-events: none;
  max-height: calc(100vh - 32px);
  overflow: hidden;
}

.toaster-module__toast___amRbV:last-child {
  z-index: 2;
}

.toaster-module__toast___amRbV:focus {
  outline: 2px solid var(--color-secondary-cobalt);
  outline-offset: 2px;
}

.toaster-module__toast___amRbV.toaster-module__success___b-uVw {
  background-color: var(--color-bg-light-emerald);
  border-color: var(--color-secondary-emerald);
}

.toaster-module__toast___amRbV.toaster-module__error___vYYFp {
  background-color: var(--color-bg-light-burgundy);
  border-color: var(--color-secondary-burgundy);
}

.toaster-module__toast___amRbV.toaster-module__info___oyzsU {
  background-color: var(--color-bg-light-cobalt);
  border-color: var(--color-secondary-cobalt);
}

.toaster-module__toast___amRbV.toaster-module__warning___L40Rl {
  background-color: var(--color-bg-light-cobalt);
  border-color: var(--color-secondary-cobalt);
}

.toaster-module__toastContent___eaghb {
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
  flex: 1;
  min-width: 0;
}

.toaster-module__toastTitle___lkHWy {
  font-weight: 600;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  margin: 0;
  color: var(--color-primary-charcoal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toaster-module__toastDescription___zKUo5 {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  margin: 0;
  color: var(--color-primary-charcoal);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.toaster-module__toastIcon___pz4LS {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

.toaster-module__toastCTA___Qafx9 {
  cursor: pointer;
  color: var(--color-secondary-cobalt);
  font-weight: bold;
  text-decoration: none;
  background: none;
  border: none;
  padding: 0;
  font-size: inherit;
  line-height: inherit;
  flex-shrink: 0;
}

.toaster-module__toastCTA___Qafx9:hover {
  text-decoration: underline;
}

.toaster-module__toastCTA___Qafx9:focus-visible {
  outline: 2px solid var(--color-secondary-cobalt);
  outline-offset: 2px;
  border-radius: 2px;
}

.toaster-module__closeButton___rCZ8I {
  cursor: pointer;
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  color: var(--color-primary-charcoal);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.toaster-module__closeButton___rCZ8I:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.toaster-module__closeButton___rCZ8I:focus-visible {
  outline: 2px solid var(--color-secondary-cobalt);
  outline-offset: 2px;
}

.toaster-module__toast___amRbV[data-state="open"] {
  animation: toaster-module__slideInRight___-LFzO 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.toaster-module__toast___amRbV[data-state="closed"] {
  animation: toaster-module__slideOutRight___YJpsM 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes toaster-module__slideInRight___-LFzO {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toaster-module__slideOutRight___YJpsM {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toaster-module__toast___amRbV:hover {
  transform: translateX(-4px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.toaster-module__bulletPoints___nPPVQ {
  margin: 0;
  padding: 0;
  list-style: none;
}

.toaster-module__bulletPoints___nPPVQ li {
  position: relative;
  padding-left: 16px;
  margin-bottom: 4px;
}

.toaster-module__bulletPoints___nPPVQ li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--color-primary-charcoal);
}

@keyframes treeView-module__rotation-clockwise___01IP- {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}

@keyframes treeView-module__rotation-counter-clockwise___hacnd {
  0% {
    transform: rotate(90deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.treeView-module__treeViewRoot___UVvqK ul {
  width: 100%;
  list-style-type: none;
  padding-inline-start: 0.75rem;
  margin: 0;
}

.treeView-module__treeViewRoot___UVvqK ul li {
  margin-bottom: 5px;
}

.treeView-module__branch___WZhT4 {
  padding: 0;
  width: 100%;
  text-align: left;
}

.treeView-module__focusableBranch___HxXDX:focus-visible {
  outline: none;

  .treeView-module__indicator___m4vFa {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: -3px;
    border-radius: 3px;
  }
}

.treeView-module__indicator___m4vFa {
  margin-bottom: 0;
  line-height: 20px;
  font-size: 14px;
  height: 44px;
  align-content: center;
  padding-right: 8px;
  padding-left: 8px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.treeView-module__disabledLink___gp59s {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.treeView-module__readOnly___rRg7A {
  padding-left: 2rem;
}

.treeView-module__indicator___m4vFa svg {
  vertical-align: middle;
}

.treeView-module__indicator___m4vFa:hover {
  cursor: pointer;
  background-color: #f2f2f2;
  max-width: 210px;
  width: fit-content;
  min-width: -webkit-fill-available;
}

.treeView-module__treeViewSelected___7N9A6 {
  background-color: #f2f2f2;
}

.treeView-module__indicator___m4vFa:focus-visible {
  background-color: #f2f2f2;
}

.treeView-module__icons___GLy-j,
.treeView-module__icons___GLy-j .treeView-module__dropdown___wCP3g {
  display: flex;
  align-items: center;
}

.treeView-module__icons___GLy-j .treeView-module__dropdown___wCP3g {
  color: #5b6e7f;
  animation: treeView-module__rotation-counter-clockwise___hacnd 0.25s linear;
}

.treeView-module__icons___GLy-j .treeView-module__dropdownRotated___9HFKj {
  transform: rotate(90deg);
  display: inline-block;
  color: #5b6e7f;
  animation: treeView-module__rotation-clockwise___01IP- 0.25s linear;
}

.treeView-module__icons___GLy-j .treeView-module__folder___q5IO- {
  margin-right: 8px;
  margin-left: 8px;
  display: inline-block;
}

.treeView-module__icons___GLy-j .treeView-module__dropdown___wCP3g:focus-visible {
  transform: rotate(90deg);
}

.treeView-module__highlighted___jZGF9 {
  background-color: #f2f2f2;
  font-weight: 600;
  width: fit-content;
  min-width: -webkit-fill-available;
}

.treeView-module__labelParent___LMWgy {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  align-content: center;
  color: var(--color-primary-light-charcoal);

  span {
    display: flex;
    align-items: center;
    white-space: nowrap;
    height: 100%;
  }

  span:hover {
    background-color: #f2f2f2;
  }
}

.treeView-module__loading___B725f {
  display: flex;
  align-items: center;
  justify-content: center;
}

.treeView-module__restrictedAndReadonly___p3cQh {
  display: flex;
  gap: 8px;
  padding-right: 4px;
}

.documentsNav-module__parentDiv___MYuMp {
  background-color: white;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.documentsNav-module__inputParent___q-oup {
  margin: 0 1rem;
  padding-top: 16px;
}

.documentsNav-module__inputParent___q-oup [data-forspacing="forSearchIcon"] {
  top: 12px;
}

.documentsNav-module__input___-3mmX {
  height: 40px;
  padding: 0 1.75rem 0 1rem;
  border-radius: 100px;
  text-overflow: ellipsis;
  font-size: 0.875rem;
  letter-spacing: -0.1px;
}

.documentsNav-module__labelParent___692E7 {
  margin: 0 1rem;
}

.documentsNav-module__label___YIMGl {
  font-family: var(--primary-font-family);
  font-size: var(--fontsize-body-label);
  line-height: var(--lineheight-body-label);
  color: #666666;
  font-weight: 600;
  text-transform: uppercase;
}

.documentsNav-module__docNavButton___SqqF6 {
  height: 44px;
  flex: 0 1 auto;
  margin: 1rem;
  cursor: pointer;
}

.documentsNav-module__documentsTitleLink___vHXwO {
  display: flex;
  align-items: center;
  font-size: var(--fontsize-body-small);
  font-weight: 400;
  gap: 0.5rem;
  padding: 0.85rem 0.5rem;
  border-radius: 2px;
  width: 100%;
  color: var(--color-primary-light-charcoal);

  &:hover {
    color: initial;
  }

  &:focus-visible {
    outline-color: var(--color-primary-pale-charcoal);
    outline-width: 0.25rem;
  }
}

.documentsNav-module__documentsHighlight___9v8TI,
.documentsNav-module__documentsTitleLink___vHXwO:hover {
  background-color: #f2f2f2;
}

.documentsNav-module__folders___mrTZ0 {
  margin-right: 16px;
  flex: 1 1 auto;
  overflow: auto;
  min-height: 0;
}

.documentsNav-module__folders___mrTZ0 li {
  display: inline;
}

.documentsNav-module__spinnerContainer___-DxxC {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.drawer-module__drawerWrapper___YDIN6 {
  position: fixed;
  top: 0;
  left: 0;
  display: grid;
  align-items: end;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-drawer);
}

.drawer-module__overlay___860-5 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  opacity: 0.5;
}

.drawer-module__drawer___5ARuA {
  display: grid;
  grid-template-rows: auto 1fr auto;
  align-content: space-between;
  min-width: 32.5rem;
  max-width: 100%;
  height: 100vh;
  background-color: var(--color-bg-white);
  box-shadow: -4px 0px 20px 0px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
}

.drawer-module__drawer___5ARuA.drawer-module__large___dv-LS {
  width: 42rem;
}

.drawer-module__drawerHeader___nCPYX {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  overflow: hidden;
  padding: 1rem;
  color: var(--color-primary-charcoal);
  border-bottom: 1px solid var(--color-primary-pale-charcoal);
  min-height: 5rem; /* subject to rework - min height to allow for spacing around absolute positioned X button and no title/subtitle */
  outline: none;

  .drawer-module__drawerHeaderLeft___S5EJ2 {
    display: flex;
    flex-direction: row;
    gap: 0.75rem;

    .drawer-module__avatar___Kb-Yo {
      height: 56px;
      width: 56px;
      border-radius: 50%;
      background-color: var(--color-secondary-ocean);
      color: var(--color-bg-white);
      margin-right: 0.75rem;
      font-size: var(--fontsize-h5);
      font-weight: 600;
      line-height: var(--lineheight-h5);
      text-align: center;
    }

    .drawer-module__backButton___aJHBC {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      padding: 0;
      margin-right: 0.25rem;
      border-radius: 50%;
      width: 2rem;
      height: 2rem;
      transition: background-color 0.2s;
      align-self: center;

      > svg {
        flex-shrink: 0;
      }

      &:hover {
        background-color: var(--color-bg-light-grey);
      }
    }
  }
}

.drawer-module__drawerHeaderContent___DUV-6 {
  align-content: center;
  width: 100%;
}

.drawer-module__drawerTitle___oZmm9 {
  font-weight: 600;
  font-size: var(--fontsize-h4);
  line-height: 2rem;
  margin: 0;
  height: 2.5rem;
  display: flex;
  align-items: center;
}

.drawer-module__drawerPretitle___z5d-y {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  color: var(--color-primary-light-charcoal);
  height: 2.5rem;
  display: flex;
  align-items: center;
}

.drawer-module__drawerSubtitle___ZWPz2 {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-primary-charcoal);
}

.drawer-module__drawerContent___EvGma {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.drawer-module__drawerFooter___qzUcs {
  display: grid;
  justify-content: end;
  grid-template-columns: 1fr 1fr;
  column-gap: 1rem;
  padding: 1rem;
  border-top: 1px solid var(--color-bg-primary-pale-charcoal);
}

.drawer-module__headerButtons___7ADKH {
  position: absolute;
  top: 24px;
  right: 24px;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.drawer-module__copyButton___-VTIy:hover {
  cursor: pointer;
}

@media (min-width: 769px) {
  .drawer-module__drawerHeader___nCPYX {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .drawer-module__drawerHeaderTitleOnly___Rfbs- {
    padding: 1.5rem 1.5rem 1.25rem 1.5rem;
  }

  .drawer-module__drawerFooter___qzUcs {
    grid-template-columns: auto auto;
    padding: 1rem 2rem;
  }
}

@media (max-width: 768px) {
  .drawer-module__backButton___aJHBC {
    display: none !important;
  }

  .drawer-module__drawer___5ARuA,
  .drawer-module__drawer___5ARuA.drawer-module__large___dv-LS {
    min-width: unset;
    width: 100vw;
  }

  .drawer-module__drawerContent___EvGma {
    width: 100vw;
  }
}

.dropdownMenu-module__dropdownMenuWrapper___Rlao7 {
  [data-scope="menu"][data-part="trigger"] {
    cursor: pointer;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.2s;
  }

  [data-scope="menu"][data-part="trigger"]:hover {
    background-color: var(--color-bg-light-grey);
  }

  [data-scope="menu"][data-part="trigger"]:focus-visible {
    border: 0;
    margin: 0;
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: -3px;
  }
}

.dropdownMenu-module__dropdownMenuPositioner___hus-7 {
  /* Override library's z-index to show above drawers */
  z-index: var(--z-index-dropdown-menu) !important;

  [data-scope="menu"][data-part="content"] {
    outline: none;
    display: block;
    border: 1px solid var(--color-bg-grey);
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
    min-width: 15rem;
    padding: 0;
    /* Temporary to fix global styles leaking from other components */
    position: static !important;
  }

  [data-scope="menu"][data-part="content"][data-state="open"] {
    display: block;
  }

  [data-scope="menu"][data-part="content"][data-state="closed"] {
    display: none;
  }

  [data-scope="menu"][data-part="item"] {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    padding: 1rem;
    gap: 1rem;
    color: var(--color-primary-charcoal);
    cursor: pointer;
    transition: background-color 300ms linear;
    text-wrap-mode: nowrap;
    height: auto;
    width: auto;

    svg {
      path {
        fill: var(--color-primary-charcoal);
      }
    }
  }

  [data-scope="menu"][data-part="item"]:first-of-type {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
  }

  [data-scope="menu"][data-part="item"]:last-of-type {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
    border-bottom: none;
  }

  [data-scope="menu"][data-part="item"] {
    &:not([data-disabled]):hover {
      background-color: var(--color-bg-light-grey);
    }

    &[data-disabled] {
      cursor: not-allowed;
      color: var(--color-bg-inactive-charcoal);
    }
  }

  [data-scope="menu"][data-part="content"][data-state="closed"] {
    opacity: 0;
    transition: opacity 200ms linear, visibility 200ms linear;
  }

  [data-scope="menu"][data-part="content"][data-state="open"] {
    background-color: var(--color-bg-white);
    opacity: 1;
    transition: opacity 200ms linear, visibility 200ms linear;
  }

  [data-scope="menu"][data-part="item"].dropdownMenu-module__critical___HETIB {
    color: var(--color-secondary-burgundy);
    border-top: 1px solid var(--color-primary-pale-charcoal);

    svg {
      path {
        fill: var(--color-secondary-burgundy);
      }
    }
  }
}

.dropdownInput-module__dropdownWrapper___JoTSR {
  display: flex;
  flex-direction: column;
  position: relative;
}

.dropdownInput-module__dropdownLabel___zBMUz {
  color: var(--color-primary-charcoal);
  line-height: 24px;
  font-weight: 600;
  font-size: var(--fontsize-default);
  margin-bottom: 1rem;
  display: block;
}

.dropdownInput-module__dropdownSelection___jf6DO {
  display: flex;
  flex-direction: column;
  align-items: baseline;
}

.dropdownInput-module__dropdownPlaceholder___iIzvp {
  color: var(--color-bg-inactive-charcoal);
}

.dropdownInput-module__dropdownPlaceholder___iIzvp[data-focus] {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  left: 1rem;
  top: 0.25rem;
  position: absolute;
}

.dropdownInput-module__dropdownControl___6BhvZ {
  position: relative;
}

.dropdownInput-module__dropdownControl___6BhvZ.dropdownInput-module__error___0HWK0 {
  border: 1px solid var(--color-primary-red);
}

.dropdownInput-module__input___zjlg- {
  height: 3.5rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-right: 1.725rem; /* Extra padding to accomodate dropdown arrow */
}

.dropdownInput-module__inputSpinner___uHl1z {
  padding-right: 2rem; /* Extra padding to accomodate spinner */
}

.dropdownInput-module__inputError___2AU4-,
.dropdownInput-module__inputError___2AU4-:focus-visible {
  border: none;
}

.dropdownInput-module__dropdownIndicator___30FqC {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.dropdownInput-module__dropdownIndicator___30FqC.dropdownInput-module__disabled___OhoVW {
  pointer-events: none;
}

.dropdownInput-module__dropdownTrigger___mk9de {
  position: relative;
  z-index: var(--z-index-dropdown-list-in-table);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0;
  border: 0;
}

.dropdownInput-module__arrowUp___U2y-A {
  transform: translateY(-50%) rotate(180deg);
}

@keyframes dropdownInput-module__fadeIn___5eDGh {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.dropdownInput-module__dropdownItemsWrapper___Gxlcs {
  position: relative;
  z-index: var(--z-index-dropdown-list-in-table);
}

.dropdownInput-module__dropDownContent___1p3ql {
  max-height: 15.625rem;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border-radius: 2px;
  border: 1px solid var(--color-primary-pale-charcoal);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  &.dropdownInput-module__largeContent___mSd-I {
    width: 20.3rem;
  }
}

.dropdownInput-module__dropdownItemsWrapper___Gxlcs.dropdownInput-module__hidden___lynPo {
  pointer-events: none;
  visibility: hidden;
}

.dropdownInput-module__dropdownPositioner___HY2Uz.dropdownInput-module__hidden___lynPo {
  visibility: hidden;
  opacity: 0;
  transition: opacity 200ms, visibility 200ms;
}

.dropdownInput-module__dropdownPositioner___HY2Uz.dropdownInput-module__visible___K5xYr {
  animation: dropdownInput-module__fadeIn___5eDGh 200ms;
}

.dropdownInput-module__dropdownItem___reXWR {
  display: flex;
  min-height: 48px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex: 1 0 0;
  gap: 0.5rem;
  padding: 0 1rem;
  background-color: var(--color-bg-white);
  color: var(--color-primary-charcoal);
  transition: background-color 200ms linear;
  border: 1px solid var(--color-primary-pale-charcoal);
  box-sizing: border-box;
  cursor: pointer;
}

.dropdownInput-module__noResults___oZ3Ue {
  padding: 1rem;
  background-color: var(--color-bg-light-grey);
  color: var(--color-primary-inactive-charcoal);
  pointer-events: none;
}

.dropdownInput-module__dropdownItem___reXWR.dropdownInput-module__itemWithDescription___Z-l5m {
  border: none;
  padding: 0.5rem 1rem;

  .dropdownInput-module__labelWithDescription___gVxUE {
    font-size: var(--fontsize-body);
    font-weight: bold;
    line-height: var(--lineheight-h6);
  }

  .dropdownInput-module__itemDescription___o25kT {
    margin-bottom: 0.5rem;
  }
}

.dropdownInput-module__dropdownItem___reXWR.dropdownInput-module__itemWithoutBorder___T919K {
  border: none;
}

.dropdownInput-module__ctaOnNoResults___kG84s {
  padding: 1rem;
  color: var(--color-secondary-cobalt);
  font-weight: bold;
}

.dropdownInput-module__dropdownItem___reXWR:hover,
.dropdownInput-module__dropdownItem___reXWR.dropdownInput-module__isSelected___4l0CX,
.dropdownInput-module__dropdownItem___reXWR[data-highlighted],
.dropdownInput-module__customOption___RZlBO.dropdownInput-module__isSelected___4l0CX {
  background-color: var(--color-bg-light-grey);
  transition: background-color 200ms linear;
}

.dropdownInput-module__customOption___RZlBO button {
  width: 100%;
}

.dropdownInput-module__titleContainer___sUKwW {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dropdownInput-module__itemDescription___o25kT {
  display: block;
  margin-left: 1.5rem;
}

.dropdownInput-module__chipContainer___OT9-Q {
  background-color: var(--color-bg-white);
  width: 100%;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 2px;
  cursor: pointer;
  padding: 0.938rem;
  line-height: 24px;
  text-align: left;

  &.dropdownInput-module__selected___p1Crd {
    border: 1px solid var(--color-primary-charcoal);
  }

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.dropdownInput-module__itemChip___f7sNi {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  border: 1px solid var(--color-primary-pale-charcoal);
  padding: 0.125rem 0.5rem;
  width: fit-content;

  &.dropdownInput-module__selectedChip___scxlN {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 1rem;
    background-color: white;
  }
}

.dropdownInput-module__errorMessage___hTG5p {
  margin-top: 0.75rem;
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
  vertical-align: center;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.dropdownInput-module__renderTop___lpFm8 {
  top: -325px !important;
}

.dropdownInput-module__isRequired___TWQ5z{
  font-size: var(--fontsize-body);
  color: var(--color-primary-red);
}

.dropdownInput-module__labelContainer___k4PuR{
  display: flex;
  gap: 3px;
}

@media (min-width: 769px) {
  .dropdownInput-module__renderTop___lpFm8 {
    top: -308px !important;
  }
}

.dropdownInput-module__dropdownScrollContainer___tfTUO {
  max-height: 15rem;
  overflow-y: auto;
}

.dropdownInput-module__fixedCTAContainer___hMBKd {
  position: sticky;
  bottom: 0;
  background-color: var(--color-bg-white);
  border-top: 1px solid var(--color-primary-pale-charcoal);
  padding: 0.5rem;
  z-index: 1;
}

.dropdownInput-module__customOption___RZlBO {
  width: 100%;
  text-align: left;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
}

.textButton-module__textButton___9U0dQ {
  background-color: transparent;
  display: flex;
  flex-direction: row;
  align-items: center;
  color: var(--color-secondary-cobalt);
  font-style: normal;
  font-weight: 600;
  text-align: center;
  border: none;
  cursor: pointer;
}

.textButton-module__textButton___9U0dQ:disabled,
.textButton-module__textButton___9U0dQ:disabled:hover {
  cursor: not-allowed;
  text-decoration: none;
  color: var(--color-bg-inactive-charcoal);
  path {
    fill: var(--color-bg-inactive-charcoal);
  }
}

.textButton-module__textButton___9U0dQ:focus-visible {
  outline: 2px solid var(--color-secondary-cobalt);
}

.textButton-module__textButton___9U0dQ.textButton-module__large___VQs4j,
.textButton-module__textButton___9U0dQ.textButton-module__medium___V2Rwv {
  gap: 0.5rem;
}

.textButton-module__textButton___9U0dQ.textButton-module__small___8HHgs {
  gap: 0.25rem;
}

.textButton-module__textButton___9U0dQ.textButton-module__large___VQs4j {
  font-size: var(--fontsize-body);
}

.textButton-module__textButton___9U0dQ.textButton-module__medium___V2Rwv {
  font-size: var(--fontsize-body-small);
}

.textButton-module__textButton___9U0dQ.textButton-module__small___8HHgs {
  font-size: var(--fontsize-body-xsmall);
}

.textButton-module__textButton___9U0dQ.textButton-module__inline___Veuxb {
  font-size: var(--fontsize-body-small);
  padding: 0;
}

.textButton-module__textButton___9U0dQ:hover {
  text-decoration: underline;
}

.textButton-module__iconWrapper___RF0W3 {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editProjectStages-module__container___hI2Tq {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 2px;
  background: var(--color-bg-white);
  gap: 0.75rem;
}

.editProjectStages-module__firstStage___v5K44 {
  align-items: center;
  background: var(--color-bg-white);
  display: flex;
  width: 100%;
  gap: 0.75rem;
  justify-content: space-between;
}

.editProjectStages-module__expandButton___n4qqb {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;

  svg {
    path {
      fill: var(--color-primary-inactive-charcoal);
    }
  }
}

.editProjectStages-module__details___-iDvU {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.editProjectStages-module__stageName___5etl4 {
  font-size: var(--fontsize-body);
  font-weight: bold;
  color: var(--color-primary-charcoal);
}

.editProjectStages-module__subStages___-zUPF {
  font-size: var(--fontsize-body-xsmall);
  color: var(--color-primary-inactive-charcoal);
}

.editProjectStages-module__statusDropdown___4eCZ- {
  width: 100%;

  .dropdown-input-field {
    height: 2.25rem;
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .input-left-icon {
    top: 0.65rem;
    left: 1rem;
  }
}

.editProjectStages-module__statusDropdownWrapper___ren5n {
  .dropdown-item {
    padding: 0.75rem 1rem;
    gap: 0;
  }

  .item-description.item-description {
    font-size: 0.875rem;
    letter-spacing: -0.1px;
    margin-bottom: 0;
    line-height: 20px;
  }

  .labelWithDescription {
    font-weight: 400;
  }
}



.editProjectStages-module__subStageContainer___RSi83 {
  width: 100%;
  padding: 0.5rem 0;
}

.editProjectStages-module__subStage___I3mw4 {
  display: flex;
  /* Keep flex-start but ensure input fields align properly */
  align-items: flex-start;
  padding: 0.75rem 0.75rem;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 4px;
  margin-bottom: 0.5rem;
  background: var(--color-bg-white);
  position: relative;
  min-height: 48px;
}

.editProjectStages-module__subStage___I3mw4 > button:last-child {
  flex: 0 0 auto;
  margin-left: auto;
}

.editProjectStages-module__substageNameField___1W2VW,
.editProjectStages-module__substageeDateField___1pdNj,
.editProjectStages-module__substageStatusField___mJPvf {
  position: relative;
  min-width: 0;
  display: block;
  min-height: 2.5rem;
  height: auto;
  padding-left: 0.75rem;
}

/* Override error styling to use consistent red color */
.editProjectStages-module__substageNameField___1W2VW .input-field.error,
.editProjectStages-module__substageNameField___1W2VW .dropdown-input-field.error {
  border-color: var(--color-primary-red) !important;
}

.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__editProjectDateInput___geE4i.editProjectStages-module__errorState___3O5HD {
  border-color: var(--color-primary-red) !important;
}

.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field.error {
  border-color: var(--color-primary-red) !important;
}

/* Hide error icons and messages */
.editProjectStages-module__substageNameField___1W2VW .error-icon,
.editProjectStages-module__substageStatusField___mJPvf .error-icon {
  display: none !important;
}

.editProjectStages-module__substageNameField___1W2VW .error-message,
.editProjectStages-module__substageStatusField___mJPvf .error-message {
  display: none !important;
}

/* Ensure consistent field heights and alignment */
.editProjectStages-module__substageNameField___1W2VW .input-field,
.editProjectStages-module__substageNameField___1W2VW .input-wrapper,
.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field,
.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__editProjectDateInput___geE4i {
  height: 2.5rem !important;
  min-height: 2.5rem !important;
  max-height: 2.5rem !important;
  display: flex;
  align-items: center;
  /* Ensure input fields align at the same baseline */
  margin-bottom: 0;
  /* Create consistent baseline for all input types */
  vertical-align: top;
  box-sizing: border-box !important;
}

/* Additional specific styling for dropdown components */
.editProjectStages-module__substageNameField___1W2VW .dropdown-input-field,
.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field,
.editProjectStages-module__substageNameField___1W2VW .dropdown-wrapper,
.editProjectStages-module__substageStatusField___mJPvf .dropdown-wrapper,
.editProjectStages-module__substageNameField___1W2VW [data-scope="select"],
.editProjectStages-module__substageStatusField___mJPvf [data-scope="select"] {
  height: 2.5rem !important;
  min-height: 2.5rem !important;
}

/* Ensure dropdown trigger buttons have consistent height */
.editProjectStages-module__substageNameField___1W2VW .dropdown-trigger,
.editProjectStages-module__substageStatusField___mJPvf .dropdown-trigger,
.editProjectStages-module__substageNameField___1W2VW [data-part="trigger"],
.editProjectStages-module__substageStatusField___mJPvf [data-part="trigger"],
.editProjectStages-module__substageNameField___1W2VW .trigger,
.editProjectStages-module__substageStatusField___mJPvf .trigger {
  height: 2.5rem !important;
  min-height: 2.5rem !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
}

.editProjectStages-module__substageNameField___1W2VW .input-wrapper,
.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-wrapper {
  margin-bottom: 0;
  height: 2.5rem !important;
  max-height: 2.5rem !important;
  font-size: 0.875rem !important;
}

/* Target all possible input text elements for consistent font size */
.editProjectStages-module__substageNameField___1W2VW * {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* Specific styling for Input component to match dropdown size */
.editProjectStages-module__substageNameField___1W2VW input {
  height: 2.5rem !important;
  min-height: 2.5rem !important;
  max-height: 2.5rem !important;
  display: flex !important;
  padding: 8px 12px !important;
  align-items: center !important;
  gap: 6px !important;
  flex: 1 0 0 !important;
  align-self: stretch !important;
  border-radius: 2px !important;
  box-sizing: border-box !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}



/* Ensure consistent padding and border styling across all input types */
.editProjectStages-module__substageNameField___1W2VW .input-field,
.editProjectStages-module__substageNameField___1W2VW .dropdown-input-field {
  display: flex !important;
  padding: 8px 12px !important;
  align-items: center !important;
  gap: 6px !important;
  flex: 1 0 0 !important;
  align-self: stretch !important;
  border-radius: 2px !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  box-sizing: border-box !important;
}

/* Status field styling to match parent stage */
.editProjectStages-module__substageStatusField___mJPvf .dropdown-input-field {
  display: flex !important;
  padding: 8px 12px !important;
  align-items: center !important;
  gap: 6px !important;
  flex: 1 0 0 !important;
  align-self: stretch !important;
  border-radius: 2px !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  box-sizing: border-box !important;
  /* Adjust padding for icon space - using balanced values */
  padding-left: 2.2rem !important;
  padding-right: 2.2rem !important;
}

/* Icon positioning for substage status field */
.editProjectStages-module__substageStatusField___mJPvf .input-left-icon {
  top: 0.75rem;
  left: 1rem;
}

/* Ensure Input component text has consistent font size */
.editProjectStages-module__substageNameField___1W2VW .input-field,
.editProjectStages-module__substageNameField___1W2VW .input-field input,
.editProjectStages-module__substageNameField___1W2VW .input-wrapper input {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-family: inherit !important;
}

/* Ensure date picker button matches other input heights */
.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__editProjectDateInput___geE4i {
  display: flex !important;
  padding: 8px 12px !important;
  align-items: center !important;
  flex: 1 0 0 !important;
  align-self: stretch !important;
  border-radius: 2px !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  box-sizing: border-box !important;
}

/* Ensure all input elements have the same baseline alignment */
.editProjectStages-module__substageNameField___1W2VW > :first-child,
.editProjectStages-module__substageStatusField___mJPvf > :first-child,
.editProjectStages-module__substageeDateField___1pdNj > :first-child {
  align-items: center;
  margin-bottom: 0;
}

/* Ensure validation messages are positioned consistently across all field types */
.editProjectStages-module__substageNameField___1W2VW .editProjectStages-module__inlineErrorMessage___yy2Uc,
.editProjectStages-module__substageeDateField___1pdNj .editProjectStages-module__inlineErrorMessage___yy2Uc,
.editProjectStages-module__substageStatusField___mJPvf .editProjectStages-module__inlineErrorMessage___yy2Uc {
  position: relative;
  top: 0.25rem; /* Small spacing from the input field */
  left: 0;
  right: 0;
  margin-top: 0.25rem;
  margin-bottom: 0;
}

/* Inline error message styling */
.editProjectStages-module__inlineErrorMessage___yy2Uc {
  color: var(--color-primary-red);
  font-size: 0.875rem;
  font-weight: 400;
  font-family: var(--primary-font-family);
  margin-top: 0.65rem;
  margin-bottom: 0;
  /* padding-left: 0.6rem; */
  line-height: 1.2;
  /* Reserve consistent height to prevent layout shift */
  min-height: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  /* Position error messages in reserved space below inputs */
  position: relative;
  flex-shrink: 0;
  /* Ensure consistent text rendering */
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.editProjectStages-module__subStageName___3-mEM {
  font-size: var(--fontsize-body-small);
  font-weight: bold;
}

.editProjectStages-module__deleteButton___zFJaK {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;

  &:hover {
    background-color: var(--color-bg-light-grey);
  }

  svg {
    width: 16px;
    height: 16px;
    path {
      fill: var(--color-primary-red);
    }
  }
}

.editProjectStages-module__addSubstageContainer___8Bh8e {
  position: relative;
  display: inline-block;
  z-index: 1001;
}

.editProjectStages-module__addSubstageButton___YeUVB {
  color: var(--color-secondary-cobalt);
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }

  svg {
    width: 12px;
    height: 12px;
    path {
      fill: var(--color-secondary-cobalt);
    }
  }
}

.editProjectStages-module__addSubstageDropdown___5DXN6 {
  position: fixed;
  background: var(--color-bg-white);
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 4px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 99999;
  min-width: 200px;
  max-width: 250px;
  overflow: visible;
  pointer-events: auto;
}

.editProjectStages-module__dropdownOption___lGUm- {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: var(--color-primary-charcoal);
  cursor: pointer;
  pointer-events: auto;
  user-select: none;

  &:hover {
    background-color: var(--color-bg-light-grey);
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.editProjectStages-module__dateTrigger___XUi2v {
  background: var(--color-bg-white);
}

.editProjectStages-module__editProjectDateInput___geE4i {
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 2px;
  box-sizing: border-box;
  color: var(--color-primary-charcoal);
  font-family: inherit;
  font-size: var(--fontsize-body);
  height: 2.5rem;
  line-height: var(--lineheight-body);
  width: 100%;
  display: flex;
  padding: 0.5rem 0.625rem 0.5rem 1rem;
  align-items: center;

  &[data-state="open"] {
    border-color: var(--color-primary-charcoal);
  }
}

.editProjectStages-module__datePlaceholder___6NSKk {
  align-self: center;
  margin-left: 0.875rem;
  font-size: var(--fontsize-body-small);
  line-height: 20px;
  font-weight: 400;
  font-family: var(--primary-font-family);
  width: 7.75rem;
  text-align: start;
}

.editProjectStages-module__dropdownArrow___Kfvf7 {
  align-content: center;
}

.editProjectStages-module__faded___Idd7y {
  color: var(--color-primary-inactive-charcoal);
}

.editProjectStages-module__dateInputError___8Avka {
  border-color: var(--color-primary-red) !important;
}

.editProjectStages-module__customSubstageRow___dp1xu {
  background-color: var(--color-bg-white);
  border: 1px solid var(--color-border-pale-grey);
}

.editProjectStages-module__errorMessage___uh1AC {
  color: var(--color-primary-red);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  position: absolute;
  top: 100%;
  left: 0;
  white-space: nowrap;
  z-index: 10;
  background: var(--color-bg-white);
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Fix datepicker z-index and positioning to appear above modal */
.editProjectStages-module__container___hI2Tq [data-scope="date-picker"][data-part="positioner"] {
  z-index: 9999 !important;
  position: fixed !important;
}

.editProjectStages-module__container___hI2Tq [data-scope="date-picker"][data-part="content"] {
  z-index: 9999 !important;
  position: relative !important;
}

/* Ensure substage datepickers also appear above modal */
.editProjectStages-module__subStageContainer___RSi83 [data-scope="date-picker"][data-part="positioner"] {
  z-index: 9999 !important;
  position: fixed !important;
}

.editProjectStages-module__subStageContainer___RSi83 [data-scope="date-picker"][data-part="content"] {
  z-index: 9999 !important;
  position: relative !important;
}

/* Drag and drop styles */
.editProjectStages-module__subStage___I3mw4 {
  cursor: grab;
  transition: all 0.2s ease;
}

.editProjectStages-module__subStage___I3mw4:active {
  cursor: grabbing;
}

.editProjectStages-module__subStage___I3mw4.editProjectStages-module__dragging___taGva {
  opacity: 0.5;
  transform: rotate(2deg);
  cursor: grabbing;
  z-index: 1000;
}

.editProjectStages-module__subStage___I3mw4.editProjectStages-module__dragOver___--I-Z {
  border-color: var(--color-secondary-cobalt);
  border-width: 2px;
  background-color: #f2f7fb;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.editProjectStages-module__dragHandle___bF7Zu {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  width: 16px;
  height: 42px;
  cursor: grab;
  color: var(--color-primary-inactive-charcoal);
  transition: color 0.2s ease;
}

.editProjectStages-module__dragHandle___bF7Zu:hover {
  color: var(--color-primary-charcoal);
}

.editProjectStages-module__subStage___I3mw4:active .editProjectStages-module__dragHandle___bF7Zu {
  cursor: grabbing;
}

.toggle-module__control___azYKw {
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  width: 2.25rem;
  height: 1.25rem;
  padding: 0.0625rem;
  border: 1px solid var(--color-border-grey);
  border-radius: 9999px;
  background-color: var(--color-bg-grey);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.toggle-module__control___azYKw:is(:checked, [data-checked], [aria-checked="true"], [data-state="checked"]) {
  border: 1px solid var(--color-primary-red);
  background-color: var(--color-primary-red);
}

.toggle-module__control___azYKw:is(:disabled, [data-disabled]) {
  border: 1px solid var(--color-border-grey);
  background-color: var(--color-bg-white);
  cursor: not-allowed;

  &[data-state="checked"],
  [data-checked],
  [aria-checked="true"],
  [data-state="checked"] {
    border: 1px solid transparent;
    background-color: var(--color-primary-red-disabled);
  }
}

.toggle-module__thumb___REMff {
  width: 1rem;
  height: 1rem;
  border-radius: 9999px;
  background-color: var(--color-bg-white);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0, 1);
}

.toggle-module__thumb___REMff:is(:checked, [data-checked], [aria-checked="true"], [data-state="checked"]) {
  transform: translateX(100%);
}

.toggle-module__thumb___REMff:is(:disabled, [data-disabled]) {
  background-color: var(--color-bg-grey);

  &[data-state="checked"],
  [data-checked],
  [aria-checked="true"],
  [data-state="checked"] {
    background-color: var(--color-bg-light-grey);
  }
}

.toggle-module__root___V61o8 {
  position: relative;
  display: grid;
  padding: 1px;
}

.toggle-module__root___V61o8:is(:focus-visible, [data-focus]) {
  border: 2px solid var(--color-secondary-cobalt);
  border-radius: 100px;
  margin: -2px;
}

.inlineMessage-module__inlineMessage___aQzHi {
  display: flex;
  gap: 1rem;
  padding: 0.75rem 1rem 1rem 1rem;
  border-left: 4px solid var(--color-secondary-gold);
  border-radius: 1px;
  box-shadow: 0px 4px 8px 0px #00000026;
  color: var(--color-primary-light-charcoal);
  align-items: center;
}

p.inlineMessage-module__inlineMessageText___lRxYi {
  font-size: var(--fontsize-body-small);
}

.inlineMessage-module__inlineMessageContent___wqxmV {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.inlineMessage-module__inlineMessageNoTitle___0lvqp {
  padding-top: 0;
}

.inlineMessage-module__inlineMessageTitle___bxmKx {
  font-size: var(--fontsize-default);
  line-height: var(--lineheight-body);
  font-weight: 600;
}

.inlineMessage-module__inlineMessageTitle___bxmKx,
.inlineMessage-module__inlineMessageText___lRxYi {
  margin: 0;
}

.inlineMessage-module__warning___zqS8v {
  border-left: 4px solid var(--color-secondary-gold);
}

.inlineMessage-module__success___ODPRZ {
  border-left: 4px solid var(--color-secondary-jade);
}

.inlineMessage-module__info___otBPt {
  border-left: 4px solid var(--color-secondary-ocean);
}

.inlineMessage-module__error___PzwoK {
  border-left: 4px solid var(--color-secondary-burgundy);
}

.comments-module__comments___8sM2I {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  background-color: var(--color-bg-white);
}

.comments-module__header___OGCpV {
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid var(--color-primary-pale-charcoal);
  border-top: 1px solid var(--color-primary-pale-charcoal);
  outline: none;
}

.comments-module__commentsContainer___KBdeL {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem 0;
}

.comments-module__comment___n8j7i {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0 2rem;
}

.comments-module__commentHeader___S-oE6 {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 1rem;
}

.comments-module__buttonsContainer___Z0wvE {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

.comments-module__newComment___AnMt- {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem 2rem;
}

.comments-module__newCommentAvatar___rr4AR {
  flex-shrink: 0;
}

.comments-module__newCommentEditor___-deDS {
  flex-grow: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.comments-module__commentData___YluMV {
  margin-top: 0;
  width: 100%;
}

.comments-module__commentContainer___dlnZV {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.comments-module__author___dbcY7 {
  color: var(--color-primary-light-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body);
  font-weight: 600;
  margin: 0;
}

.comments-module__timestamp___vyeng {
  color: var(--color-bg-inactive-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body);
  font-weight: 400;
  margin: 0;
}

.comments-module__title___mo-8H {
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 600;
}

.comments-module__commentText___hdr1J {
  color: var(--color-primary-light-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body);
  white-space: preserve-breaks;
}

.comments-module__noCommentsContainer___Koujq {
  padding: 1rem;
}

.comments-module__noCommentsSection___j8e3- {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1.5rem;
  border-radius: 2px;
  background-color: var(--color-primary-soft-charcoal);

  svg {
    color: var(--color-bg-primary);
  }
}

.comments-module__noCommentsText___sM1dK {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-primary-inactive-charcoal);
  font-weight: 600;
}

.comments-module__spinner___Keuve {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 2rem;
}

.comments-module__addCommentButton___nQbK- {
  button {
    &:hover {
      background-color: var(--color-primary-slate);
    }
  }
}

.toolbar-module__toolbar___bWE5w {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--color-bg-white);
  border-bottom: 1px solid var(--color-border-grey);
}

.toolbar-module__button___7QhCD {
  font-family: var(--primary-font-family);
  background-color: transparent;
  border: 1px solid var(--color-border-grey);
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
}

.toolbar-module__button___7QhCD:hover {
  background-color: var(--color-bg-primary-pale-charcoal);
  border-color: var(--color-border-primary-charcoal);
}

.toolbar-module__button___7QhCD.toolbar-module__active___Be41P {
  background-color: var(--color-bg-primary-pale-charcoal);
  border-color: var(--color-border-primary-charcoal);
}

:root {
  --footer-actions-height: 2.5rem;
}

.wysywygeditor-module__wrapper___wR5YK {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.wysywygeditor-module__editorContainer___uR1nw {
  position: relative;
  border: 1px solid var(--color-primary-pale-charcoal);
  border-radius: 4px;
  padding: 0.5rem;
  width: 100%;
  background-color: var(--color-bg-white);

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.wysywygeditor-module__editorContainer--hasContent___SLNRm {
  border: 1px solid var(--color-primary-charcoal);
  background-color: var(--color-bg-white);
}

.wysywygeditor-module__editorContainer___uR1nw:focus-within {
  border: 1px solid var(--color-primary-charcoal);

  &.focusedByKeyboard {
    outline: 2px solid var(--color-secondary-cobalt);
  }
}

.wysywygeditor-module__editorContainer--error___SDjzK,
.wysywygeditor-module__editorContainer--error___SDjzK:focus-within {
  border: 1px solid var(--color-secondary-burgundy);
}

.wysywygeditor-module__contentEditable___H2Gda {
  font-size: var(--fontsize-body);
  outline: none;
  min-height: 1.5rem;
  max-height: calc(20rem - var(--footer-actions-height));
  overflow: auto;
}

.wysywygeditor-module__contentEditable___H2Gda.wysywygeditor-module__smallText___TbgXK {
  font-size: var(--fontsize-body-small);
}

.wysywygeditor-module__placeholder___rMn1Z {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  pointer-events: none;
  color: var(--color-primary-inactive-charcoal);
  line-height: var(--lineheight-body);
  font-size: var(--fontsize-body-small);
}

.wysywygeditor-module__htmlEditor___qzpoE {
  width: 100%;
  height: 200px;
  font-family: monospace;
  font-size: var(--fontsize-body);
  line-height: 1.5;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 4px;
  padding: 0.75rem;
  box-sizing: border-box;
  resize: none;
  outline: none;
  background-color: var(--color-bg-white);
  color: var(--color-primary-charcoal);
}

.wysywygeditor-module__htmlEditor___qzpoE:focus {
  outline: none;

  &.focusedByKeyboard {
    border-color: var(--color-secondary-cobalt);
    box-shadow: 0 0 3px rgba(0, 119, 204, 0.8);
  }
}

.wysywygeditor-module__footerActions___kbybp {
  display: flex;
  justify-content: end;
  gap: 1rem;
  height: var(--footer-actions-height);
}

.wysywygeditor-module__characterCount___JXPxz {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  color: var(--color-primary-inactive-charcoal);
  margin-top: 0.5rem;
}

.wysywygeditor-module__characterCountError___huEaM {
  color: var(--color-secondary-burgundy);
}

.editor-placeholder {
  color: #999;
  font-style: italic;
  pointer-events: none;
}

.editor-paragraph {
  margin: 0;
  color: var(--color-primary-light-charcoal);
  line-height: var(--lineheight-body);
  font-size: inherit;
}

.editor-list-ul {
  list-style-type: disc;
  margin-left: 20px;
}

.editor-listitem {
  margin-bottom: 0.5rem;
}

.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.mention-at {
  background-color: var(--color-bg-primary-pale-charcoal);
  color: var(--color-bg-primary-charcoal);
  padding: 2px 4px;
  border-radius: 4px;
}

.mention-at-focused {
  background-color: var(--color-bg-primary-pale-charcoal);
  color: var(--color-bg-primary-charcoal);
}

.mention-hash {
  /* Placeholder Color */
  background-color: #e81a3b3b;
  color: var(--color-primary-red);
  padding: 2px 4px;
  border-radius: 4px;
}

.mention-hash-focused {
  /* Placeholder Color */
  background-color: #e81a3b3b;
  color: var(--color-primary-red);
}

.mentionmenu-module__mentionsMenu___c6r4z {
  margin: 0;
  padding: 8px;
  background: var(--color-primary-white);
  border: 1px solid var(--color-bg-primary-pale-charcoal);
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  width: 300px;
  list-style: none;
}

.mentionmenu-module__mentionsLoading___sRo4F {
  padding: 8px;
  color: var(--color-bg-primary-pale-charcoal);
  font-style: italic;
  text-align: center;
}

.mentionmenu-module__mentionsItem___HoQiJ {
  padding: 8px 12px;
  font-size: var(--fontsize-body-xsmall);
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.mentionmenu-module__mentionsItem___HoQiJ:hover {
  background-color: var(--color-bg-primary-pale-charcoal);
  color: black;
}

.mentionmenu-module__mentionsItemSelected___XEwGm {
  background-color: var(--color-secondary-cobalt);
  color: var(--color-primary-white);
}

.inviteUserCard-module__card___luRb0 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--color-border-light-grey);
}

.inviteUserCard-module__card___luRb0:first-of-type {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.inviteUserCard-module__card___luRb0:last-of-type {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.inviteUserCard-module__card___luRb0:hover {
  background-color: var(--color-bg-light-grey);
}

.inviteUserCard-module__userInfo___qoTl9 {
  display: flex;
  flex-direction: column;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body);
}

.inviteUserCard-module__userName___9uzgQ {
  font-weight: 600;
  color: var(--color-primary-charcoal);
}

.inviteUserCard-module__userEmail___6tbGe {
  color: var(--color-primary-inactive-charcoal);
}

.inviteUserCard-module__unassignedUser___A2wXc {
  border-radius: 50%;
  background-color: var(--color-bg-grey);
  padding: 0.25rem;
}

.menuitem-module__link___mNcXf {
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  color: var(--color-bg-white);
  font-size: 14px;
  padding: 0.75rem;
  border-radius: 2px;
  transition: background-color 0.2s;

  &:hover {
    color: var(--color-bg-white);
    background-color: var(--color-primary-light-charcoal);
  }

  &:focus-visible {
    outline-color: var(--color-outline-dark-bg);
    outline-offset: -3px;
  }
}

.menuitem-module__link___mNcXf i {
  margin-right: 8px;
  font-size: 18px;
}

.menuitem-module__active___O2srh {
  border-left: 1px solid #e81a3b;
}

.menuitem-module__active___O2srh {
  color: var(--color-bg-white);
  background-color: var(--color-primary-light-charcoal);
  border-radius: 2px;
}

.menuitem-module__longLabel___HsARv {
  white-space: normal;
  word-break: break-word;
  hyphens: auto;
}

.modal-module__modalWrapper___HEHwS {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: end;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-modal);
}

.modal-module__overlay___PR8ec {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  opacity: 0.5;
}

.modal-module__truncateTitle___12esQ {
  white-space: nowrap;
  overflow: hidden;
  max-width: 400px;
  text-overflow: ellipsis;
  word-wrap: normal;
  word-break: normal;
  hyphens: none;
}

.modal-module__modal___u3COr {
  width: 100%;
  position: relative;
  max-height: 80%;
  z-index: 1;
  background-color: var(--color-bg-white);
  box-shadow: 0px 4px 4px 0px #00000040;
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
}

.modal-module__modalContent___22CDu {
  display: flex;
  flex-direction: column;
  height: 65vh;
  min-height: 0;
}

.modal-module__modalHeader___TxZbn {
  display: grid;
  align-items: center;
  padding: 1.5rem 1.5rem 0;
}

.modal-module__modalHeader--withBorder___B5pEv {
  border-bottom: 1px solid var(--color-border-pale-grey);
  padding-bottom: 1rem;
}

.modal-module__modalHeaderTextContainer___0lMRZ {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modal-module__modalTitleContainer___GKoJq {
  display: flex;
  align-items: center;
  min-width: 0; 
  flex: 1;
}

.modal-module__modalTitle___J9EuI {
  color: var(--color-primary-charcoal);
  margin: 0;
  font-size: var(--fontsize-h4);
  line-height: var(--lineheight-h4);
  font-weight: 600;
  outline: none;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

.modal-module__padTitle___KJreS {
  padding-right: 3rem; /* Compensate for delete button overlap */
}

.modal-module__modalSubtitle___9xEUS {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 400;
  color: var(--color-primary-light-charcoal);
}

.modal-module__informationContainer___xMFW5 {
  display: grid;
  grid-template-columns: auto auto;
  justify-content: start;
  align-items: center;
  column-gap: 0.5rem;
  color: var(--color-bg-inactive-charcoal);
}

.modal-module__informationContainer--error___38TFm {
  color: var(--color-secondary-burgundy);
}

.modal-module__informationText___A-SAd {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  color: var(--color-primary-light-charcoal);
}

.modal-module__informationText--error___ttidD {
  color: var(--color-secondary-burgundy);
}

.modal-module__slottedContent___y-QlK {
  max-height: 25rem;
  padding: 1.5rem;
  height: 100%;
  flex: 1; 
  min-height: 0;
}

.modal-module__sectionedContent___eW8uD {
  max-height: 2.75rem;
  padding: 0.75rem;
}

.modal-module__overflow___qSCWP {
  overflow: auto;
}

.modal-module__scroll___lvg-6 {
  overflow-y: auto;
  min-height: 20rem;
  max-height: 20rem;
}

.modal-module__modalFooter___478rR {
  display: flex;
  flex-direction: row-reverse;
  position: relative;
  background-color: var(--color-bg-white);
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--color-border-pale-grey);
  gap: 1rem;
}

.modal-module__modalFooter--start___-q0-Q {
  justify-content: start;
}

.modal-module__modalFooter--end___B5Qoq {
  justify-content: end;
}

.modal-module__modalFooter--between___WNRak {
  justify-content: space-between;

  button {
    width: 100%;
  }
}

.modal-module__infoButton___HZTCB {
  background-color: transparent;
  border: none;
  display: flex;
  border-radius: 50px;
}

.modal-module__infoButton___HZTCB:hover {
  cursor: pointer;
  background-color: var(--color-primary-pale-charcoal);
}

.modal-module__DocModal___oNx31 {
  display: flex;
  flex-direction: row-reverse;
  position: relative;

  .modal-module__modalHeader___TxZbn {
    padding: 16px 16px 0 16px;
  }

  .modal-module__modalTitle___J9EuI {
    color: var(--color-primary-charcoal);
    font-size: var(--fontsize-body);
    font-family: var(--primary-font-family);
    font-weight: 600;
    line-height: var(--lineheight-body);
  }

  .modal-module__modalSubtitle___9xEUS {
    color: var(--color-bg-inactive-charcoal);
    font-family: var(--fontsize-body-small);
    font-weight: 600;
    line-height: var(--lineheight-body);
    font-size: var(--fontsize-body-xsmall);
  }

  .modal-module__informationContainer___xMFW5 {
    color: var(--color-primary-charcoal);
    width: 268px;
    border-bottom: 1px solid var(--color-border-pale-grey);
  }

  .modal-module__informationText___A-SAd {
    padding-bottom: 12px;
  }

  .modal-module__slottedContent___y-QlK {
    padding: 12px 0 8px 16px;
  }

  .modal-module__chipStyling___S9Fr5 {
    padding-top: 8px;
    padding-bottom: 12px;
    width: fit-content;
  }

  .modal-module__bottomBorder___xGiMk {
    width: 268px;
    border-bottom: 1px solid var(--color-border-pale-grey);
    height: 1px;
    margin-bottom: 12px;
  }

  .modal-module__overlay___PR8ec {
    width: 0;
    height: 0;
    display: flex;
  }

  .modal-module__modalWrapper___HEHwS {
    width: auto;
    height: auto;
    left: auto;
    top: auto;
    display: flex;
    flex-direction: row-reverse;
    transform: translate(35px, 26px);
    position: absolute;
  }

  .modal-module__manageAccess___t3qxY {
    font-weight: 600;
    font-size: var(--fontsize-body-small);
    color: var(--color-secondary-cobalt);
    padding-top: 8px;
    width: fit-content;
  }

  .modal-module__manageAccess___t3qxY:hover {
    cursor: pointer;
  }
}

.modal-module__newFolderButton___QwE1O {
  position: absolute;
  display: flex;
  gap: 0.5rem;
  top: 40px;
  left: 30px;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  color: var(--color-secondary-cobalt);
  padding: 0;

  &:hover {
    text-decoration: underline;
  }
}

.modal-module__slottedContentNoHeaderBorder___2Wr9y {
  padding: 0.5rem 1.5rem 1.5rem 1.5rem;
}

@media (min-width: 769px) {
  .modal-module__modalWrapper___HEHwS {
    align-items: center;
  }

  .modal-module__modal___u3COr {
    margin: 0 2rem;
    max-height: 50rem;
    border-radius: 2px;
  }

  .modal-module__modalHeader___TxZbn {
    padding: 2rem 2rem 0;
  }

  .modal-module__modalHeader--withBorder___B5pEv {
    padding-bottom: 1.5rem;
  }

  .modal-module__modalContent___22CDu {
    display: grid;
    height: initial;
    min-height: 0;
  }

  .modal-module__modalLarge___JKgcE {
    width: 49rem;
    max-width: 100%;
  }

  .modal-module__modalMedium___XefdF {
    width: 37.5rem;
    max-width: 100%;
  }

  .modal-module__modalSmall___pj6qT {
    width: 35rem;
    max-width: 100%;
  }

  .modal-module__modalMini___7LNvT {
    width: 18.75rem;
    max-width: 100%;
  }

  .modal-module__slottedContent___y-QlK {
    padding: 1rem 1.5rem;
  }

  .modal-module__slottedContentNoHeaderBorder___2Wr9y {
    padding: 1rem 2rem 2rem;
  }

  .modal-module__modalFooter___478rR {
    padding: 1.5rem 2rem;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px;
  }

  .modal-module__modalFooter--between___WNRak {
    button {
      width: auto;
    }
  }
}

.modal-module__closeModalButton___tBdl7 {
  position: absolute;
  top: 2rem;
  right: 2rem;
}

.notificationTypeBadge-module__notificationTypeBadge___AuyV5 {
  padding: 0.125rem 0.5rem;
  align-items: center;
  border-radius: 100px;
  width: fit-content;
}

.notificationTypeBadge-module__notificationTypeBadgeText___fFRXj {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  font-family: var(--primary-font-family);
  color: var(--color-primary-light-charcoal);
}

.progressBar-module__progressContainer___ibF48 {
  display: flex;
  gap: 8px;
}

.progressBar-module__progressSection___rY7dm {
  height: 0.25rem;
  background-color: var(--color-primary-pale-charcoal);
}

.progressBar-module__merged___I2yWf {
  height: 0.25rem;
  background-color: var(--color-primary-red);
  margin-left: -8px;
}

.progressBar-module__merged___I2yWf:first-of-type {
  margin-left: 0px;
}

@media (min-width: 769px) {
  .progressBar-module__progressSection___rY7dm {
    width: 3rem;
    height: 0.5rem;
  }
  
  .progressBar-module__active___ESJ6E {
    background-color: var(--color-primary-red);
    position: relative;

    & + .progressBar-module__active___ESJ6E {
      &:before {
        content: "";
        position: absolute;
        width: 8px;
        height: 8px;
        background-color: var(--color-primary-red);
        top: 0;
        left: -8px;
      }
    }
  }
}

.progressCompletion-module__container___zzhpF {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 1rem;
  background-color: var(--color-bg-white);
  font-family: var(--primary-font-family);
}

.progressCompletion-module__header___jRfpe {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progressCompletion-module__title___SEWuS {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary-charcoal);
  margin: 0;
  line-height: var(--lineheight-body);
}

.progressCompletion-module__infoIcon___0Vmh- {
  font-size: 0.875rem;
  cursor: pointer;
}

.progressCompletion-module__editContainer___p5VeB {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  color: var(--color-secondary-cobalt);
  font-weight: 600;
  cursor: pointer;
  padding: 0;

  &:hover {
    text-decoration: underline;
  }
}

.progressCompletion-module__lastUpdatedContainer___couy8 {
  margin: 1rem 0 2rem 0;
  color: var(--color-primary-light-charcoal);
}

.progressCompletion-module__progressWrapper___N8-H6 {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  justify-content: space-between;
}

.progressCompletion-module__percentage___EIx-P {
  font-size: 1.875rem;
  font-weight: 600;
  margin-right: 0.5rem;
}

.progressCompletion-module__modalChildren___D1wRq {
  display: flex;
  gap: 1.5rem;
}

.progressCompletion-module__progressBarContainer___HcbzG {
  flex-grow: 1;
  height: 12px;
  border-radius: 4px;
  background-color: var(--color-bg-primary-pale-charcoal);
  overflow: hidden;
  position: relative;
  float: left;
  width: 100%;
  margin-top: 21px;
}

.progressCompletion-module__progressBarContainerModal___ShD9C {
  width: 80%;
}

.progressCompletion-module__progressBar___gAcCv {
  height: 100%;
  background-color: var(--color-secondary-cobalt);
  position: absolute;
  top: 0;
  left: 0;
}

.progressCompletion-module__endDateContainer___tQFOg {
  font-size: 1rem;
  color: var(--color-primary-inactive-charcoal);
  text-align: right;
}

.progressCompletion-module__endDate___7jqiQ {
  font-weight: 600;
}

.progressCompletion-module__modalChildren___D1wRq {
  display: flex;
  gap: 1.5rem;
}

.progressCompletion-module__inputWrapper___lTTvh {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 20%;
}

.progressCompletion-module__successToast___nak0U {
  position: absolute;
  bottom: 1rem;
  right: 2rem;
}

.searchDropdown-module__fieldRoot___-mAUi {
  width: 100%;
}

.searchDropdown-module__userSelectInput___8M-uF {
  position: relative;
  display: flex;
  align-items: center;
  border: 0.0625rem solid var(--color-border-pale-grey);
  border-radius: 2px;
  padding: 0.5rem 1rem;
  background-color: var(--color-bg-white);
  transition: background-color 0.2s;

  &::placeholder {
    color: var(--color-bg-inactive-charcoal);
  }

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }

  &:focus-within {
    outline: none;

    &.focusedByKeyboard {
      outline: 2px solid var(--color-secondary-cobalt);
    }
  }
}

.searchDropdown-module__useMaxWidth___3HJ30 {
  width: 100%;
}

.searchDropdown-module__userSelectInput___8M-uF:focus-visible,
.searchDropdown-module__userSelectInput___8M-uF[data-state="focus"] {
  outline: none;
  border: 1px solid var(--color-primary-charcoal);

  &.focusedByKeyboard {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }
}

.searchDropdown-module__userSelectInputError___JL6zZ,
.searchDropdown-module__userSelectInputError___JL6zZ[data-state="focus"] {
  border-color: var(--color-secondary-burgundy);
}

.searchDropdown-module__selectedUsers___zPv1Q {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  width: 100%;
  max-height: 7.5rem;
  overflow-y: auto;
}

.searchDropdown-module__label___n0OSj {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  min-width: 1rem;
  flex-grow: 1;
}

.searchDropdown-module__unassignedUser___m3BqN {
  border-radius: 50%;
  background-color: var(--color-bg-grey);
  padding: 0.25rem;
}

.searchDropdown-module__input___9VFCR {
  background-color: transparent;
  color: var(--color-primary-charcoal);
  border: none;
  outline: none;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  padding: 0;
  width: 0;
  min-width: 100%;
  font-family: var(--primary-font-family);
}

.searchDropdown-module__dropdown___7YvLu {
  position: absolute;
  background-color: var(--color-bg-white);
  border-radius: 0.25rem;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  max-height: 11.75rem;
  overflow-y: auto;
  width: 100%;
  z-index: 10;
  top: 100%;
  left: 0;
  list-style: none;
  padding: 0;
}

.searchDropdown-module__dropdownItem___jNiDy {
  cursor: pointer;
  font-size: 1rem;
  color: var(--color-primary-charcoal);
  transition: background-color 0.2s;

  &[data-highlighted] {
    background-color: var(--color-bg-light-grey);
  }
}

.searchDropdown-module__dropdownItem___jNiDy:hover {
  background-color: var(--color-bg-light-grey);
}

.searchDropdown-module__noResults___a1PnC {
  padding: 0.5rem;
  color: var(--color-primary-charcoal);
  font-size: 1rem;
  text-align: center;
}

.searchDropdown-module__errorTextContainer___iDysJ {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-secondary-burgundy);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  margin-top: 0.75rem;
}

.searchDropdown-module__errorText___x3kug {
  margin: 0;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body);
}

.sectionBanner-module__sectionBanner___MkOWU {
  width: 100%;
  padding: 0.5rem 1.5rem;
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body-eyebrow);
  line-height: var(--lineheight-body-eyebrow);
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 2px;
  background-color: var(--color-bg-light-grey);
  letter-spacing: 0.1rem;
}

.sectionBanner-module__sectionBanner--highlighted___RAuTJ {
  background-color: #0062b81a;
}

.selectionCard-module__selectionCard___OSNPH {
  display: grid;
  grid-template-columns: auto 1fr auto;
  justify-items: start;
  width: 100%;
  padding: 1.5rem 1rem;
  margin: 0;
  border-radius: 2px;
  border: 1px solid var(--color-border-pale-grey);
  background-color: var(--color-bg-white);
  cursor: pointer;
  transition: background-color 0.5s ease;
}

.selectionCard-module__selectionCard___OSNPH:hover {
  background-color: var(--color-bg-light-grey);
}

.selectionCard-module__selectionCard--charcoal___EZjWG {
  background-color: var(--color-bg-light-grey);
}

.selectionCard-module__selectionCard--charcoal___EZjWG:hover {
  background-color: var(--color-bg-white);
}

.selectionCard-module__title___3VDlJ {
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 600;
  text-align: left;
}

.selectionCard-module__subtitle___7gg3q {
  color: var(--color-primary-light-charcoal);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  margin-top: 0.5rem;
  text-align: start;
}

.selectionCard-module__leftIconContainer___ENTXb {
  margin-right: 1rem;
  width: 1.5rem;
  height: 1.5rem;

  svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}

.selectionCard-module__textContainer___0BThx {
  display: grid;
  justify-items: start;
}

.selectionCard-module__rightIconContainer___p69yQ {
  justify-self: end;
  align-self: center;
  margin-left: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  color: var(--color-secondary-cobalt);

  svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}

.selectionCard-module__selectionCardDisabled___yTmaG {
  cursor: not-allowed;
  background: var(--color-bg-light-grey);

  .selectionCard-module__title___3VDlJ, .selectionCard-module__subtitle___7gg3q {
    color: var(--color-primary-inactive-charcoal);
  }
}

.selectionCard-module__burgundy___RT7RF {
  color: var(--color-secondary-burgundy);
}

.selectionCard-module__emerald___cQ4s- {
  color: var(--color-secondary-emerald);
}

.selectionCard-module__ocean___hoLQm {
  color: var(--color-secondary-ocean);
}

.selectionCard-module__gold___v7SCz {
  color: var(--color-secondary-gold);
}

.selectionCard-module__jade___x2qfD {
  color: var(--color-secondary-jade);
}

.splitter-module__splitter___xXqus {
  display: flex;
  width: 100%;
  height: 100%;
}

.splitter-module__panel___iNFgO {
  overflow: auto;
  padding: 0;
  height: 100%;
}

.splitter-module__resizeTrigger___WsF1E {
  background-color: #e5e5e5;
  cursor: col-resize;
  padding: 0;
  position: relative;
  transition: background-color 0.2s ease;
  width: 2px;
  border: 0;
  border-radius: 0;
}

.splitter-module__resizeTrigger___WsF1E:hover {
  background-color: var(--color-primary-charcoal);
}

.splitter-module__resizeTrigger___WsF1E:active {
  background-color: var(--color-primary-charcoal);
}

/* Visual indicator when resizing */
.splitter-module__resizeTrigger___WsF1E::after {
  content: "";
  position: absolute;
  top: 0;
  left: -2px;
  right: -2px;
  bottom: 0;
  background: transparent;
}

.splitter-module__resizeTrigger___WsF1E:hover::after {
  background-color: rgba(var(--color-primary-charcoal-rgb), 0.1);
}

.statusBadge-module__statusBadge___GhPoU,
.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm .statusBadge-module__statusBadge___GhPoU,
.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm [data-scope="menu"][data-part="trigger"] {
  position: relative;
  display: grid;
  grid-template-columns: auto 1fr auto;
  column-gap: 0.5rem;
  padding: 0.125rem 0.5rem 0.125rem 0.25rem;
  justify-content: start;
  align-items: center;
  background-color: var(--color-primary-white);
  border: 1px solid var(--color-primary-pale-charcoal);
  border-radius: 100px;
  color: var(--color-primary-slate);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  transition: 100ms ease-out;
  width: max-content;

  &.statusBadge-module__clickable___NtlLj {
    transition: background-color 0.2s;

    &:hover {
      background-color: var(--color-bg-light-grey);
    }

    &:focus-visible {
      outline: 2px solid var(--color-secondary-cobalt);
      outline-offset: 2px;
    }
  }
}

.statusBadge-module__statusBadge___GhPoU[disabled] {
  cursor: unset;
}

.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm
  div[data-part="positioner"][id="menu:actionItemBadge:popper"] {
  position: relative !important;
  transform: translate3d(0px, 2px, 0) !important;
}

.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm
  div[id="menu:actionItemBadge:content"][data-state="open"][aria-labelledby="menu:actionItemBadge:trigger"] {
  z-index: var(--z-index-chip-dropdown);
}

.statusBadge-module__statusBadgeText___MwNbg {
  color: var(--color-primary-charcoal);
  font-family: "ProximaNova", Arial, sans-serif;
}

.statusBadge-module__statusIconContainer___-nt0J {
  padding: 0.25rem;
  border-radius: 50%;
}

.statusBadge-module__itemContainer___UF5lo[data-scope="menu"][data-part="item"] {
  display: flex;
  align-items: flex-start;
  padding: 1rem 1rem;
  width: 100%;
  justify-content: flex-start;
}

.statusBadge-module__itemContainer___UF5lo:hover {
  background-color: var(--color-bg-light-grey);
  cursor: pointer;
}

.statusBadge-module__itemTextContainer___ZRjXa {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 40px;
  width: max-content;
}

.statusBadge-module__itemLabel___R-RHf {
  font-size: var(--fontsize-body);
}

.statusBadge-module__itemText___vErL5 {
  font-size: var(--fontsize-body-small);
  color: var(--color-primary-inactive-charcoal);
}

/* TODO: make these not affect globally */

[data-scope="menu"][data-part="positioner"] {
  display: contents;
}

[data-scope="menu"][data-part="content"] {
  outline: none;
  display: block;
  border: 1px solid var(--color-border-grey);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  padding: 0;
  position: relative;
  z-index: var(--z-index-dropdown-list);
  position: absolute;
}

[data-scope="menu"][data-part="content"][data-state="closed"] {
  visibility: hidden;
  opacity: 0;
  transition: opacity 200ms linear, visibility 200ms linear;
}

[data-scope="menu"][data-part="content"][data-state="open"] {
  background-color: var(--color-bg-white);
  visibility: visible;
  opacity: 1;
  transition: opacity 200ms linear, visibility 200ms linear;
}

[data-scope="menu"][data-part="item"] {
  cursor: pointer;
  display: flex;
  padding: 0 1rem;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  color: var(--color-primary-charcoal);
  font-family: "ProximaNova", Arial, sans-serif;
  font-size: var(--fontsize-body);
  font-style: normal;
  font-weight: 400;
  line-height: var(--lineheight-body);
}

[data-scope="menu"][data-part="item"].statusBadge-module__labelWithIcon___MQBH5 {
  border: none;
  padding-left: 8px;
}

[data-scope="menu"][data-part="item"].statusBadge-module__critical___DcpfC {
  color: var(--color-secondary-burgundy);
}

.statusBadge-module__withIcon___zYtDG {
  display: flex;
  flex-direction: row;
  border: transparent;
}

.statusBadge-module__withIcon___zYtDG:hover {
  cursor: pointer;
  background-color: var(--color-primary-pale-charcoal);

  [data-scope="menu"][data-part="item"] {
    background-color: var(--color-primary-pale-charcoal);
  }
}

.statusBadge-module__forIcon___dZSzw {
  display: flex;
  flex-direction: column;
  margin-top: 0.75rem;
}

.statusBadge-module__actionItemStatusBadgeWrapper___9DFEm
  > [id="menu:actionItemBadge:popper"][aria-expanded="true"] {
  display: none;
}

.statusBadge-module__statusIconContainer___-nt0J {
  position: relative;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  opacity: 0;
}

.statusBadge-module__statusIcon___odKXG {
  position: absolute;
  top: 7px;
  left: 7px;
}

.statusBadge-module__statusIconLarge___OUxGn {
  top: 4px;
  left: 4px;
}

.statusBadge-module__statusIconContainerWithPulse___J-TTm {
  animation: statusBadge-module__pulse___dx6ck 2.75s infinite;
  position: relative;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  opacity: 0;
}

@keyframes statusBadge-module__pulse___dx6ck {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.25);
    opacity: 1;
  }
  75% {
    transform: scale(1.25);
    opacity: 0;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

.statusChip-module__statusChip___zn5qj {
  display: flex;
  flex-direction: row;
  gap: 0.25rem;
  padding: 0.125rem 0.5rem 0.125rem 0.25rem;
  justify-content: start;
  align-items: center;
  background-color: var(--color-primary-white);
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 100px;
  color: var(--color-primary-slate);
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  transition: 100ms ease-out;
}

.statusChip-module__statusChip___zn5qj:hover {
  cursor: pointer;
  background-color: var(--color-bg-light-grey);
  border-color: var(--color-border-light-grey);
}

.statusChip-module__statusChipText___A4pxm {
  color: var(--color-primary-charcoal);
  font-family: "ProximaNova", Arial, sans-serif;
}

.tab-module__tab___VaffT {
  padding: 0.75rem 0.5rem;
  text-decoration: none;
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  font-weight: 600;
  color: var(--color-bg-inactive-charcoal);
  border-bottom: 3px solid transparent;
  cursor: pointer;
  border-radius: 0;
  display: inline-block;
}

.tab-module__tab___VaffT:hover {
  color: var(--color-bg-inactive-charcoal);
  background-color: var(--color-bg-light-grey);
}

.tab-module__tab___VaffT:focus-visible {
  outline: 2px solid var(--color-secondary-cobalt);
  outline-offset: 2px;
}

.tab-module__tab___VaffT.tab-module__active___nUiBm {
  color: var(--color-primary-charcoal);
  border-bottom: 3px solid var(--color-primary-red);
}

.table-module__tableContainer___S5x22 {
  border-radius: 8px;
  border: 1px solid var(--color-primary-pale-charcoal);
  background-color: var(--color-bg-white);
  width: 100%;
  height: 100%;
}

.table-module__containerNoScroll___Pa668 {
  overflow: hidden;
}

.table-module__containerWithScroll___IZpfR {
  overflow: auto;
}

.table-module__table___l4qQP {
  width: 100%;
  border-collapse: collapse;
  border: none;
  margin: 0;
  flex: 1;
}

.table-module__table___l4qQP thead {
  position: sticky;
  top: 0;
  background-color: #f5f5f5;
  z-index: var(--z-index-table-header);
}

/* Fix for rendering bug where a pixel peeks underneath */
.table-module__table___l4qQP thead::before {
  content: "";
  display: block;
  position: absolute;
  background-color: #f5f5f5;
  left: 0;
  width: 100%;
  height: 2px;
  top: -1px;
}

.table-module__tableHeader___kN6-K,
.table-module__tableCell___SkfZ4 {
  padding: 0.75rem;
  border: none;
}

.table-module__tableRow___G2VsC {
  border-bottom: 1px solid var(--color-primary-pale-charcoal);

  [data-scope="menu"][data-part="trigger"] {
    background-color: transparent;
  }

  [data-scope="menu"][data-part="trigger"]:hover {
    background-color: var(--color-bg-light-grey);
  }
}

.table-module__tableRow___G2VsC:hover {
  background-color: var(--color-primary-soft-charcoal);
}

.table-module__rowClick___pVnJK {
  cursor: pointer;

  &:focus-visible {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: -2px;
  }
}

.table-module__tableHeader___kN6-K {
  text-align: left;
  padding: 0.75rem;
  background-color: #f5f5f5;
  color: var(--color-primary-light-charcoal);
  font-size: var(--fontsize-body-xsmall);
  text-transform: uppercase;
  border-bottom: 1px solid var(--color-primary-pale-charcoal);
  white-space: nowrap;
}

.table-module__tableHeaderCell___FDHwr,
.table-module__selectCell___Rbe8l,
.table-module__titleCell___nZURE,
.table-module__filesCell___QzP3M {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: var(--primary-font-family);
}

.table-module__tableHeaderCell___FDHwr button {
  font-weight: 600;
  color: var(--color-primary-light-charcoal);
  border: 0;
  padding: 0.75rem;
  width: -webkit-fill-available;
  margin: -0.75rem;
  border-radius: 0;
  transition: 0.2s background-color;

  &:focus-visible {
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: -3px;
  }

  &:hover {
    background-color: var(--color-primary-pale-charcoal);
  }
}

.table-module__selectHeader___NE0-c,
.table-module__selectTableCell___IAkUv,
.table-module__selectTrashHeader___x1Cq0,
.table-module__selectTrashTableCell___MWlSU {
  width: 0; /* Zero-width makes cell fit content */
}

.table-module__modifiedCell___6cy-i {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
  white-space: nowrap;
  color: var(--color-primary-light-charcoal);
}

.table-module__actions___r7Gos {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-left: auto;
}

.table-module__notificationActivityTableCell___fIfz6 {
  height: 3.75rem;
}

.table-module__nameCell___tgc6- {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  margin-left: -8px;
}

.table-module__taskName___KOWB5 {
  font-weight: bold;
  color: var(--color-primary-light-charcoal);
}

.table-module__tag___qoPrA {
  display: inline-block;
  background: #f0f0f0;
  color: var(--color-primary-charcoal);
  font-size: var(--fontsize-body-xsmall);
  padding: 2px 8px;
  border-radius: 4px;
}

.table-module__statusCell___OZAjv {
  display: flex;
  align-items: center;
}

.table-module__statusCircle___rU7aP {
  height: 10px;
  border-radius: 50%;
  background: var(--color-secondary-cobalt);
  margin-right: 8px;
}

.table-module__dueDateCell___nqiOB,
.table-module__modifiedCell___6cy-i {
  color: var(--color-primary-light-charcoal);
}

.table-module__assigneeCell___EMvA1 {
  display: flex;
  gap: 4px;
}

.table-module__assignee___E-7r1,
.table-module__avatar___ks7Qf,
.table-module__initials___qV4GG {
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--fontsize-body-xsmall);
  font-weight: bold;
}

.table-module__assignee___E-7r1 {
  background: var(--color-secondary-cobalt);
  color: white;
}

.table-module__initials___qV4GG {
  background: #888;
  color: white;
  font-size: 0.85rem;
}

.table-module__selectCell___Rbe8l {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.table-module__titleCell___nZURE {
  flex-grow: 1;
  color: var(--color-primary-light-charcoal);
  font-weight: 600;
}

.table-module__filesCell___QzP3M {
  color: var(--color-bg-inactive-charcoal);
  font-weight: 400;
}

.table-module__selectCellIcon___ifaKX {
  padding: 0.5rem;
  background-color: var(--color-bg-light-grey);
}

.table-module__sortableHeader___5tBDk {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-module__emptyMessageContainer___NREXM {
  display: flex;
  flex-direction: column;
  color: var(--color-bg-inactive-charcoal);
  padding: 2rem;
  margin: 1rem;
  border-radius: 2px;
  align-items: center;
  justify-content: center;
  height: 90%;
  gap: 0.5rem;
}

.table-module__emptyMessageText___JakoD {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.table-module__emptyMessageTitle___W6KOd {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  font-weight: 600;
}

.table-module__emptyMessageSubMessage___CloDq {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
}

.table-module__clientTableHeader___swCAz {
  gap: 0.5rem;
}

.table-module__clientTableCell___zV6Uc {
  padding: 1.125rem 0.75rem;
}

.table-module__trashActionItemHeader___-OgTq,
.table-module__trashActionItemCell___2xDCo {
  display: none;
}
.table-module__placeholderItemRow___RBokn {
  cursor: not-allowed;
  border-bottom: 1px solid var(--color-primary-pale-charcoal);
}

.table-module__placeholderSelectCell___amf2f {
  pointer-events: none;
}

.table-module__skeletonItemCell___hWhGb {
  position: relative;
  overflow: hidden;
  background-color: var(--color-bg-light-grey);
  border-radius: 4px;
  height: 1rem;
  width: 80%;
}

.table-module__skeletonItemCell___hWhGb::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 80%;
  background-image: linear-gradient(
    90deg,
    rgba(242, 242, 242, 0) 0%,
    rgba(255, 255, 255, 0.35) 50%,
    rgba(242, 242, 242, 0) 100%
  );
  background-size: 200% 100%;
  background-repeat: no-repeat;
  animation: table-module__shimmer___4Fq2C 1.2s infinite linear;
}

.table-module__iconCell___YgnBx {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0.5rem;
}

.table-module__placeholderItem___3zsJT {
  padding: 0.75rem;
}

.table-module__placeholderItemCell___TFNwd {
  display: flex;
  gap: 1rem;
}

.table-module__placeholderItemText___Mbyzh {
  display: flex;
  flex-direction: column;
  opacity: 0.5;
}

.table-module__placeholderItemLoadingText___hg3P4 {
  font-size: var(--fontsize-body-small);
}

.table-module__highlightImportantRow___okwvf {
  background-color: var(--color-bg-warning-yellow-translucent);
}

@media (min-width: 1280px) {
  .table-module__trashActionItemHeader___-OgTq,
  .table-module__trashActionItemCell___2xDCo {
    display: table-cell;
  }
}

.table-module__hideOnSmallScreen___ngiom,
.table-module__hideOnMediumScreen___ZmVav {
  display: none;
}

@media (min-width: 768px) {
  .table-module__hideOnSmallScreen___ngiom {
    display: table-cell;
  }
}

@media (min-width: 1024px) {
  .table-module__hideOnMediumScreen___ZmVav {
    display: table-cell;
  }
}

.table-module__teamMemberTableCell___hYBA7 {
  padding: 0.5rem 1rem;
}

@keyframes table-module__shimmer___4Fq2C {
  0% {
    background-position: -150% 0;
  }
  100% {
    background-position: 150% 0;
  }
}

.tabs-module__tabsContainerWrapper___jOEVs {
  position: relative;
  width: 100%;
  scrollbar-width: none;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 0 2rem;

  @media (max-width: 768px) {
    overflow-x: auto;
  }
}

.tabs-module__tabsContainerWrapper___jOEVs::-webkit-scrollbar {
  display: none;
}

.tabs-module__tabsContainer___6Ie58 {
  display: flex;
  gap: 10px;
  min-width: max-content;
}

[data-scope="menu"][data-part="trigger"] {
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  justify-content: center;
  column-gap: 0.5rem;
  outline: none;
  border: none;
  padding: 0.5rem;
  border-radius: 2px;
  margin: 0;
  background-color: var(--color-primary-white);
  color: var(--color-primary-slate);
  visibility: visible;
  position: relative;
}

/* TODO: Finish the implementation of quick add post-MVP */
/* See ticket 8091 */
/* Based on existing TODO on the ProjectTasksTable.tsx line 37 */
/* .parent [data-scope="menu"][data-part="trigger"]:hover,
[data-scope="menu"][data-part="trigger"]:focus {
  background-color: var(--color-bg-light-grey);
  cursor: pointer;
} */

/* [data-scope="menu"][data-part="trigger"]:focus {
  border: 1px solid var(--color-secondary-cobalt);
  margin: -1px;
} */

/* Removing since it is affecting global styling for other components */
/* [data-scope="menu"][data-part="trigger"][data-state="open"] {
  background-color: var(--color-bg-light-grey);
}

[data-scope="menu"][data-part="trigger"][data-state="open"] {
  background-color: var(--color-bg-light-grey);
} */

/* TODO: make these not affect globally */

[data-scope="menu"][data-part="positioner"] {
  display: block;
}

[data-scope="menu"][data-part="content"] {
  border-radius: 2px;
  box-shadow: 0px 4px 4px 0px #00000040;
  padding: 0.5rem 0;
}

[data-scope="menu"][data-part="content"]:focus-visible {
  outline: none;
}

[data-scope="menu"][data-part="item"] {
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 1rem;
  align-items: center;
  font-size: var(--fontsize-body);
  line-height: var(--fontsize-body);
  color: var(--color-primary-charcoal);
  width: max-content;
}

[data-scope="menu"][data-part="item"]:hover,
[data-scope="menu"][data-part="item"][data-highlighted] {
  background-color: var(--color-bg-light-grey);
  cursor: pointer;
}

.textarea-module__container___IsgwQ {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  position: relative;
}

.textarea-module__hiddenLabel___0KxD1 {
  display: none;
}

.textarea-module__label___FFa15 {
  font-weight: 600;
  line-height: var(--lineheight-h6);
  font-size: var(--fontsize-body);
}

.textarea-module__maxLength___d-THn {
  color: var(--color-primary-inactive-charcoal);
  line-height: var(--lineheight-body);
  position: absolute;
  right: 0;
}

.textarea-module__textArea___9YNgE {
  box-sizing: border-box;
  resize: none;
  overflow: hidden;
  font-size: var(--fontsize-body);
  font-family: var(--primary-font-family);
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border-pale-grey);
  border-radius: 2px;
  min-height: 80px;
  background-color: var(--color-bg-white);
  color: var(--color-primary-charcoal);
  transition: border-color 0.3s ease, box-shadow 0.3s ease,
    background-color 0.2s;

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.textarea-module__textArea___9YNgE::placeholder {
  color: var(--color-primary-inactive-charcoal);
}

.textarea-module__textArea___9YNgE:focus-visible {
  outline: none;
  border: 1px solid var(--color-primary-charcoal);

  &.focusedByKeyboard {
    border: 1px solid var(--color-border-pale-grey);
    outline: 2px solid var(--color-secondary-cobalt);
    outline-offset: 2px;
  }
}

.textarea-module__textArea___9YNgE.textarea-module__error___PKCB3 {
  border-color: var(--color-secondary-burgundy);
}

.textarea-module__textArea___9YNgE.textarea-module__success___BFQ5o {
  border-color: var(--color-secondary-jade);
}

.textarea-module__info___aGE-M {
  display: flex;
  flex-direction: row;
  gap: 3px;
  font-size: var(--fontsize-body-small);
  color: var(--color-primary-charcoal);
}

.textarea-module__requiredStar___vibR0{
  font-size: var(--fontsize-body);
  color: var(--color-primary-red);
}

.textarea-module__errorMessage___hkv1f {
  color: var(--color-secondary-burgundy);
}

.textCarousel-module__textCarousel___ZlAlw {
  display: flex;
  width: 450px;
  padding: 47px 16px 16px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  border-radius: 2px;
  border: 1px solid var(--color-border-grey);
  background-color: var(--color-bg-white);
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.16);
  overflow: hidden;
  position: relative;

  .textCarousel-module__didYouKnow___xX493 {
    position: absolute;
    top: 0;
    left: 1rem;
    font-size: var(--fontsize-body-eyebrow);
    font-style: normal;
    font-weight: 600;
    line-height: var(--lineheight-body-eyebrow);
    letter-spacing: 1px;
    text-transform: uppercase;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 12px;
    justify-content: center;
    gap: 4px;
    border-radius: 0px 0px 8px 8px;
    background: linear-gradient(
        0deg,
        rgba(26, 99, 175, 0.2) 0%,
        rgba(26, 99, 175, 0.2) 100%
      ),
      #fff;
  }

  .textCarousel-module__carouselItems___hwglz {
    .textCarousel-module__carouselItem___l7Mt4 {
      margin-right: 1rem;

      .textCarousel-module__itemText___kiP2i {
        margin-top: 0;
        margin-bottom: 0.5rem;
        font-size: var(--fontsize-h5);
        line-height: var(--lineheight-h5);
        font-weight: 600;
      }

      .textCarousel-module__itemSource___1sllB {
        color: var(--color-primary-slate);
        font-size: var(--fontsize-body-small);
        font-style: normal;
        font-weight: 400;
        line-height: var(--lineheight-body-small);
        letter-spacing: -0.1px;
      }
    }
  }

  .textCarousel-module__indicators___8zX73 {
    display: flex;
    flex-direction: row;
    gap: 12px;

    .textCarousel-module__indicator___Pxzb1 {
      width: 24px;
      height: 8px;
    }

    .textCarousel-module__indicator___Pxzb1[data-state="active"] {
      background-color: var(--color-primary-red);
    }

    .textCarousel-module__indicator___Pxzb1[data-state="inactive"] {
      background-color: var(--color-border-grey);
    }
  }
}

.projectTimeline-module__container___dw722 {
  background-color: var(--color-bg-white);
  padding: 1rem;
  border-radius: 0.25rem;
  border: 1px solid var(--color-primary-pale-charcoal);
}

.projectTimeline-module__header___8uyuJ {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: space-between;
}

.projectTimeline-module__editContainer___xNbAf {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  color: var(--color-secondary-cobalt);
  font-weight: 600;
  cursor: pointer;
  padding: 0;

  &:hover {
    text-decoration: underline;
  }
}

.projectTimeline-module__legend___rIekh {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
}

.projectTimeline-module__legendTitle___LhDC6 {
  color: var(--color-secondary-cobalt);
  font-weight: 600;
  text-align: end;
}

.projectTimeline-module__legend___rIekh p {
  font-size: 1rem;
  margin-top: 0;
}

.projectTimeline-module__timeline___CibE- {
  padding-top: 1.5rem;
  padding-left: 0.5rem;
  padding-right: 1rem;
}

.projectTimeline-module__stageWrapper___kknp9 {
  width: 100%;
}

.projectTimeline-module__stage___CV8Sp {
  display: flex;
}

.projectTimeline-module__subStageTrigger___h1sMH {
  text-align: left;
  width: 100%;
  padding: 0.35rem 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--color-primary-soft-charcoal);
  }
}

.projectTimeline-module__title___29J3R {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary-charcoal);
  margin: 0;
  line-height: var(--lineheight-body);
}

.projectTimeline-module__info___xB8Cw {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.projectTimeline-module__stageTitle___3kR3O {
  font-weight: 600;
  color: var(--color-primary-charcoal);
}

.projectTimeline-module__stageSubtitle___qLoeQ {
  margin: 0;
  color: var(--color-primary-inactive-charcoal);
  font-weight: 400;
}

.projectTimeline-module__subStageSubtitle___bDU6A {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--color-primary-inactive-charcoal);
  font-weight: 400;
}

.projectTimeline-module__icon___qSiod,
.projectTimeline-module__sub-stage-icon___639Tt {
  position: relative;
  padding-top: 0.75rem;
}

.projectTimeline-module__icon___qSiod div[aria-label="Icon"],
.projectTimeline-module__sub-stage-icon___639Tt div[aria-label="Icon"] {
  padding: 0.25rem 0;
  background-color: white;
}

.projectTimeline-module__accordionTrigger___prk26 {
  align-self: baseline;
  margin-top: 1rem;
  padding: 0;
}

.projectTimeline-module__subStageIcon___1e-Mv {
  padding-top: 1rem;
}

.projectTimeline-module__finalStage___O0-a5 {
  position: relative;
  margin-top: 0.75rem;
}

.projectTimeline-module__finalStage___O0-a5 div[aria-label="Icon"] {
  padding: 0.25rem 0;
  background-color: white;
}

.projectTimeline-module__icon___qSiod::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 100%;
  background-color: var(--color-bg-grey);
  left: 50%;
  transform: translateX(-50%);
}

.projectTimeline-module__lastUpdated___KGAya {
  margin-top: 0.5rem;
}
.projectTimeline-module__tooltipWrapper___DoXgB {
  position: relative;
  display: flex;
}

.projectTimeline-module__tooltipTrigger___a-f4- {
  display: inline-block;
}

.projectTimeline-module__tooltipCard___Zpc31 {
  position: absolute;
  z-index: 300;
  margin: 0;
  background-color: white;
  width: 20rem;
  right: -4rem;
  top: -0.5rem;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
}

.projectTimeline-module__textBlock___M870a {
  margin-left: 0.5rem;
}

.projectTimeline-module__timelineStatus___rjr5G {
  margin: 1rem 1rem 0.5rem 1rem;
  font-weight: 600;
  cursor: default;
}

.projectTimeline-module__statusContainer___jtdhh {
  display: flex;
  padding: 0.75rem 1rem 0.75rem;
}

.projectTimeline-module__statusContainer___jtdhh:last-child {
  padding-bottom: 1.25rem;
}

.projectTimeline-module__statusLabel___q-nsG {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #333333;
}

.projectTimeline-module__statusDescription___89mWR {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.1px;
  vertical-align: middle;
  color: #666666;
}

.projectTimeline-module__statusIcon___0WqMX {
  padding-top: 0.25rem;
  padding-right: 0.75rem;
}

.toast-module__toast___VzLw4 {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.65rem 1rem;
  border-radius: 2px;
  color: var(--color-primary-charcoal);
  cursor: pointer;
  opacity: 0.9;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
  margin-top: 1rem;
}

.toast-module__toast___VzLw4.toast-module__show___4kh3U {
  transform: translateX(0);
  opacity: 1;
}

.toast-module__toast___VzLw4.toast-module__hide___wlkku {
  transform: translateX(100%);
  opacity: 0;
}

.toast-module__toastContainer___ezIsR {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  width: 100%;
}

.toast-module__toastText___30bSc {
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
}

.toast-module__toastTitle___9nL48 {
  font-weight: 600;
}

.toast-module__toastCTA___8E4yf {
  cursor: pointer;
  color: var(--color-secondary-cobalt);
  font-weight: bold;
}

.toast-module__toastTitle___9nL48,
.toast-module__toastMessage___V1XKp {
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  word-break: break-word;
  margin: 0;
}

.toast-module__toastMessage___V1XKp {
  display: flex;
  gap: 1rem;
}

.toast-module__success___cuSIT {
  background-color: var(--color-bg-light-emerald);
  border: 1px solid var(--color-secondary-emerald);
}

.toast-module__error___eKgSe {
  background-color: var(--color-bg-light-burgundy);
  border: 1px solid var(--color-secondary-burgundy);
}

.toast-module__info___ABLCP {
  background-color: var(--color-bg-light-cobalt);
  border: 1px solid var(--color-secondary-cobalt);
}

@media (max-width: 768px) {
  .toast-module__toast___VzLw4.toast-module__show___4kh3U {
    transform: translateY(0);
    opacity: 1;
  }

  .toast-module__toast___VzLw4.toast-module__hide___wlkku {
    transform: translateY(100%);
    opacity: 0;
  }
}

@media (min-width: 768px) {
  .toast-module__toast___VzLw4 {
    max-width: 700px;
  }
}

.toggleListItem-module__toggleListItem___HVP-i {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
}

.toggleListItem-module__toggleListItemWithBorder___wSssQ {
  border-bottom: 1px solid var(--color-border-light-grey);
}

.toggleListItem-module__toggleListInfo___wKXno {
  margin-right: 1rem;
}

.toggleListItem-module__titleContainer___0Mrv7 {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: var(--color-secondary-cobalt);
  font-weight: 400;
}

.toggleListItem-module__title___I9cUl {
  margin-right: 0.5rem;
  font-size: var(--fontsize-body);
  line-height: var(--lineheight-body);
  color: var(--color-primary-charcoal);
}

.toggleListItem-module__subtitle___wjFnm {
  font-size: var(--fontsize-body-small);
  line-height: var(--lineheight-body-small);
  color: var(--color-primary-light-charcoal);
}

.userSelect-module__container___MJlZe {
  position: relative;
  width: 100%;
}

.userSelect-module__container___MJlZe [data-part="input"] {
  display: block;
  width: 100%;
  font-family: var(--primary-font-family);
  font-size: var(--fontsize-body);
  color: var(--color-primary-charcoal);
  border-radius: 2px;
  border: 1px solid var(--color-bg-primary-pale-charcoal);
  background-color: var(--color-primary-white);
  padding: 0.5rem 1rem;
  height: 2.5rem;
  transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;
}

.userSelect-module__container___MJlZe [data-part="input"]::placeholder {
  color: var(--color-primary-inactive-charcoal);
}

.userSelect-module__container___MJlZe [data-part="input"]:hover {
  background-color: var(--color-primary-soft-charcoal);
}

.userSelect-module__container___MJlZe [data-part="input"]:focus,
.userSelect-module__container___MJlZe [data-part="input"][data-state="open"] {
  border-color: var(--color-primary-charcoal);
  outline: none;
}

.userSelect-module__container___MJlZe [data-part="input"]:focus-visible.focusedByKeyboard {
  outline: 2px solid var(--color-secondary-cobalt);
  outline-offset: 2px;
}

.userSelect-module__container___MJlZe [data-part="control"] {
  position: relative;
}

.userSelect-module__container___MJlZe .userSelect-module__selectedAvatar___xvQiJ {
  font-weight: bold;
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.userSelect-module__container___MJlZe .userSelect-module__selectedAvatar___xvQiJ + [data-part="input"] {
  padding-left: 3rem;
}

.userSelect-module__positioner___mBs40 {
  /* Override Ark UI z-index on element */
  z-index: var(--z-index-dropdown-menu) !important;
}

.userSelect-module__content___GgJJ2 {
  background-color: var(--color-bg-white);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  max-height: 12rem;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 2px;
}

.userSelect-module__content___GgJJ2 [data-part="item"] {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: var(--color-primary-charcoal);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.userSelect-module__content___GgJJ2 [data-highlighted] {
  background-color: var(--color-bg-light-grey);
  cursor: pointer;
}

.userSelect-module__noResults___X1gAU {
  padding: 0.75rem 1rem;
  color: var(--color-primary-charcoal);
  text-align: center;
}

.userSelect-module__visuallyHidden___69iLr {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

/*# sourceMappingURL=uikit.cjs.development.css.map */