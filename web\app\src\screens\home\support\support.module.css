.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  align-items: center;
}

.header {
  overflow: visible;
  width: 100%;
  border-bottom: 1px solid var(--color-primary-pale-charcoal);
  display: flex;
  background-color: var(--color-bg-white);
  position: relative;
  padding: 24px 32px 16px 32px;

  .pageTitleWrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .pageTitle {
    margin: 0;
    font-size: var(--fontsize-h4);
    line-height: var(--lineheight-h4);
    font-weight: 600;
  }
}

.body {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 24px;
  background-color: var(--color-bg-white);
  border-radius: 4px;
  border: 1px solid var(--color-primary-pale-charcoal);
  flex: 1;
  margin: 32px 0;

  .title {
    font-size: var(--fontsize-body);
    line-height: var(--lineheight-h5);
    font-weight: 600;
    margin: 0;
  }

  .description {
    font-size: var(--fontsize-body);
    line-height: var(--lineheight-body);
    margin: 0;
  }

  .requiredText {
    font-size: var(--fontsize-body-small);
    color: var(--color-primary-light-charcoal);
    font-weight: 400;
  }

  .asterisk {
    font-size: var(--fontsize-body);
    color: var(--color-secondary-burgundy);
    margin-left: 3px;
  }

  .form ul {
    display: flex;
    flex-direction: column;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 1rem;
  }

  .form li {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    
  }

  .form li > div {
    width: 100%;
  }
}

.footer {
  width: 100%;
  display: flex;
  justify-content: center;
  background-color: var(--color-bg-white);
  border-top: 1px solid var(--color-primary-pale-charcoal);

  .footerButton {
    display: flex;
    justify-content: flex-end;
    width: 80%;
    padding: 24px 0px;
  }
}

@media (max-width: 768px) {
  .body {
    width: 90%;
    padding: 16px;

    .form li {
      flex-direction: column;
    }
  }

  .footer {
    .footerButton {
      width: 90%;
    }
  }
}